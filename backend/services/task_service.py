"""
任务管理服务
Task Management Service
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from taskiq import TaskiqResult

from ..models.task import (
    Task,
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskList,
    TaskFilter,
    TaskStats,
    TaskType,
    TaskStatus,
    TaskPriority,
)
from ..tasks import broker, get_task_config
from ..tasks.animation_tasks import generate_animation_task, batch_animation_generation
from ..tasks.conversation_tasks import process_conversation_message, batch_conversation_processing


class TaskService:
    """任务管理服务"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def create_task(self, task_data: TaskCreate) -> TaskResponse:
        """创建新任务"""
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 获取任务配置
            config = get_task_config(task_data.task_type.value)
            
            # 创建数据库记录
            task = Task(
                id=uuid.uuid4(),
                task_id=task_id,
                task_type=task_data.task_type.value,
                task_name=task_data.task_name,
                description=task_data.description,
                priority=task_data.priority.value,
                conversation_id=task_data.conversation_id,
                message_id=task_data.message_id,
                user_id=task_data.user_id,
                input_data=task_data.input_data,
                metadata=task_data.metadata,
                queue_name=task_data.queue_name or config.get("queue", "default"),
                max_retries=task_data.max_retries,
                estimated_duration=task_data.estimated_duration,
                status=TaskStatus.PENDING.value,
                created_at=datetime.utcnow(),
            )
            
            self.db.add(task)
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"Created task: {task_id} ({task_data.task_type.value})")
            
            return TaskResponse.from_orm(task)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create task: {e}")
            raise
    
    async def get_task(self, task_id: str) -> Optional[TaskResponse]:
        """获取任务详情"""
        try:
            stmt = select(Task).where(Task.task_id == task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()
            
            if not task:
                return None
            
            return TaskResponse.from_orm(task)
            
        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            raise
    
    async def update_task(self, task_id: str, update_data: TaskUpdate) -> Optional[TaskResponse]:
        """更新任务"""
        try:
            stmt = select(Task).where(Task.task_id == task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()
            
            if not task:
                return None
            
            # 更新字段
            update_dict = update_data.dict(exclude_unset=True)
            for field, value in update_dict.items():
                if field == "status" and isinstance(value, TaskStatus):
                    setattr(task, field, value.value)
                else:
                    setattr(task, field, value)
            
            # 自动设置时间戳
            if update_data.status == TaskStatus.RUNNING and not task.started_at:
                task.started_at = datetime.utcnow()
            elif update_data.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                if not task.completed_at:
                    task.completed_at = datetime.utcnow()
                
                # 计算实际执行时间
                if task.started_at and not task.actual_duration:
                    duration = (task.completed_at - task.started_at).total_seconds()
                    task.actual_duration = int(duration)
            
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"Updated task: {task_id}")
            
            return TaskResponse.from_orm(task)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update task {task_id}: {e}")
            raise
    
    async def list_tasks(self, filter_params: TaskFilter) -> TaskList:
        """获取任务列表"""
        try:
            # 构建查询条件
            conditions = []
            
            if filter_params.task_type:
                conditions.append(Task.task_type == filter_params.task_type.value)
            
            if filter_params.status:
                conditions.append(Task.status == filter_params.status.value)
            
            if filter_params.priority:
                conditions.append(Task.priority == filter_params.priority.value)
            
            if filter_params.conversation_id:
                conditions.append(Task.conversation_id == filter_params.conversation_id)
            
            if filter_params.user_id:
                conditions.append(Task.user_id == filter_params.user_id)
            
            if filter_params.is_system_task is not None:
                conditions.append(Task.is_system_task == filter_params.is_system_task)
            
            # 计算总数
            count_stmt = select(func.count(Task.id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))
            
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 构建排序
            sort_column = getattr(Task, filter_params.sort_by, Task.created_at)
            if filter_params.sort_order == "desc":
                sort_column = desc(sort_column)
            
            # 分页查询
            offset = (filter_params.page - 1) * filter_params.page_size
            stmt = select(Task).order_by(sort_column).offset(offset).limit(filter_params.page_size)
            
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            result = await self.db.execute(stmt)
            tasks = result.scalars().all()
            
            # 转换为响应模型
            task_responses = [TaskResponse.from_orm(task) for task in tasks]
            
            # 计算分页信息
            has_next = offset + len(tasks) < total
            has_prev = filter_params.page > 1
            
            return TaskList(
                tasks=task_responses,
                total=total,
                page=filter_params.page,
                page_size=filter_params.page_size,
                has_next=has_next,
                has_prev=has_prev,
            )
            
        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            raise
    
    async def submit_animation_task(
        self,
        conversation_id: str,
        message_id: str,
        text: str,
        character_id: str = "default",
        context: Dict[str, Any] = None,
        user_id: str = None,
    ) -> TaskResponse:
        """提交动画生成任务"""
        try:
            # 创建任务记录
            task_data = TaskCreate(
                task_type=TaskType.ANIMATION_GENERATION,
                task_name=f"Generate animation: {text[:50]}...",
                description=f"Generate animation for: {text}",
                conversation_id=conversation_id,
                message_id=message_id,
                user_id=user_id,
                input_data={
                    "text": text,
                    "character_id": character_id,
                    "context": context or {},
                },
            )
            
            task_response = await self.create_task(task_data)
            
            # 提交到 taskiq
            taskiq_task = await generate_animation_task.kiq(
                conversation_id=conversation_id,
                message_id=message_id,
                text=text,
                character_id=character_id,
                context=context,
                user_id=user_id,
            )
            
            # 更新任务状态
            await self.update_task(
                task_response.task_id,
                TaskUpdate(
                    status=TaskStatus.QUEUED,
                    metadata={"taskiq_task_id": taskiq_task.task_id}
                )
            )
            
            logger.info(f"Submitted animation task: {task_response.task_id}")
            return task_response
            
        except Exception as e:
            logger.error(f"Failed to submit animation task: {e}")
            raise
    
    async def submit_conversation_task(
        self,
        conversation_id: str,
        message_id: str,
        content: str,
        message_type: str = "user",
        context: Dict[str, Any] = None,
        user_id: str = None,
    ) -> TaskResponse:
        """提交对话处理任务"""
        try:
            # 创建任务记录
            task_data = TaskCreate(
                task_type=TaskType.CONVERSATION_PROCESSING,
                task_name=f"Process message: {content[:50]}...",
                description=f"Process conversation message: {content}",
                conversation_id=conversation_id,
                message_id=message_id,
                user_id=user_id,
                input_data={
                    "content": content,
                    "message_type": message_type,
                    "context": context or {},
                },
            )
            
            task_response = await self.create_task(task_data)
            
            # 提交到 taskiq
            taskiq_task = await process_conversation_message.kiq(
                conversation_id=conversation_id,
                message_id=message_id,
                content=content,
                message_type=message_type,
                context=context,
                user_id=user_id,
            )
            
            # 更新任务状态
            await self.update_task(
                task_response.task_id,
                TaskUpdate(
                    status=TaskStatus.QUEUED,
                    metadata={"taskiq_task_id": taskiq_task.task_id}
                )
            )
            
            logger.info(f"Submitted conversation task: {task_response.task_id}")
            return task_response
            
        except Exception as e:
            logger.error(f"Failed to submit conversation task: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            # 更新数据库状态
            task_response = await self.update_task(
                task_id,
                TaskUpdate(
                    status=TaskStatus.CANCELLED,
                    is_cancelled=True,
                    completed_at=datetime.utcnow()
                )
            )
            
            if not task_response:
                return False
            
            # TODO: 取消 taskiq 任务
            # 这需要根据 taskiq 的具体实现来处理
            
            logger.info(f"Cancelled task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            raise
    
    async def get_task_stats(self, user_id: str = None) -> TaskStats:
        """获取任务统计"""
        try:
            conditions = []
            if user_id:
                conditions.append(Task.user_id == user_id)
            
            base_query = select(func.count(Task.id))
            if conditions:
                base_query = base_query.where(and_(*conditions))
            
            # 总任务数
            total_result = await self.db.execute(base_query)
            total_tasks = total_result.scalar()
            
            # 各状态任务数
            status_counts = {}
            for status in TaskStatus:
                status_query = base_query.where(Task.status == status.value)
                if conditions:
                    status_query = status_query.where(and_(*conditions))
                
                result = await self.db.execute(status_query)
                status_counts[status.value] = result.scalar()
            
            # 平均执行时间
            avg_duration_query = select(func.avg(Task.actual_duration)).where(
                Task.actual_duration.isnot(None)
            )
            if conditions:
                avg_duration_query = avg_duration_query.where(and_(*conditions))
            
            avg_result = await self.db.execute(avg_duration_query)
            average_duration = avg_result.scalar()
            
            # 成功率
            completed_tasks = status_counts.get(TaskStatus.COMPLETED.value, 0)
            failed_tasks = status_counts.get(TaskStatus.FAILED.value, 0)
            finished_tasks = completed_tasks + failed_tasks
            
            success_rate = (completed_tasks / finished_tasks * 100) if finished_tasks > 0 else 0
            
            return TaskStats(
                total_tasks=total_tasks,
                pending_tasks=status_counts.get(TaskStatus.PENDING.value, 0),
                running_tasks=status_counts.get(TaskStatus.RUNNING.value, 0),
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                cancelled_tasks=status_counts.get(TaskStatus.CANCELLED.value, 0),
                average_duration=average_duration,
                success_rate=success_rate,
            )
            
        except Exception as e:
            logger.error(f"Failed to get task stats: {e}")
            raise
