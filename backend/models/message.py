"""
消息模型
Message Models
"""

from datetime import datetime
from enum import Enum
from typing import Any, Optional

from beanie import Document
from pydantic import BaseModel, Field
from pymongo import IndexModel
from bson import ObjectId


class MessageType(str, Enum):
    """消息类型枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"
    ERROR = "error"


class MessageStatus(str, Enum):
    """消息状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Message(Document):
    """消息 MongoDB 文档模型"""

    # 关联信息
    conversation_id: ObjectId = Field(..., description="对话ID")

    # 消息内容
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: MessageType = Field(default=MessageType.USER, description="消息类型")
    status: MessageStatus = Field(default=MessageStatus.PENDING, description="消息状态")

    # 元数据
    metadata: dict[str, Any] = Field(default_factory=dict, description="元数据")
    attachments: list[dict[str, Any]] = Field(default_factory=list, description="附件")

    # 处理信息
    processing_time: Optional[int] = Field(None, description="处理时间（毫秒）")
    token_count: int = Field(default=0, description="token数量")

    # 任务关联
    task_id: Optional[str] = Field(None, description="任务ID")
    agent_run_id: Optional[str] = Field(None, description="代理运行ID")

    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    # 标记
    is_deleted: bool = Field(default=False, description="是否已删除")
    is_edited: bool = Field(default=False, description="是否已编辑")

    class Settings:
        name = "messages"
        indexes = [
            IndexModel([("conversation_id", 1), ("created_at", 1)]),
            IndexModel([("message_type", 1)]),
            IndexModel([("status", 1)]),
            IndexModel([("task_id", 1)]),
            IndexModel([("is_deleted", 1)]),
            IndexModel([("content", "text")]),  # 文本搜索索引
        ]

    def mark_completed(self, processing_time: Optional[int] = None):
        """标记消息为已完成"""
        self.status = MessageStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        if processing_time is not None:
            self.processing_time = processing_time

    def mark_failed(self, error_message: str, error_code: Optional[str] = None):
        """标记消息为失败"""
        self.status = MessageStatus.FAILED
        self.error_message = error_message
        self.error_code = error_code
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()


class MessageCreate(BaseModel):
    """创建消息请求模型"""
    conversation_id: str = Field(..., description="对话ID")
    content: str = Field(..., min_length=1, description="消息内容")
    message_type: MessageType = Field(default=MessageType.USER, description="消息类型")
    metadata: dict[str, Any] = Field(default_factory=dict, description="元数据")
    attachments: list[dict[str, Any]] = Field(default_factory=list, description="附件")


class MessageUpdate(BaseModel):
    """更新消息请求模型"""
    content: Optional[str] = Field(None, min_length=1, description="消息内容")
    status: Optional[MessageStatus] = Field(None, description="消息状态")
    metadata: Optional[dict[str, Any]] = Field(None, description="元数据")
    processing_time: Optional[int] = Field(None, description="处理时间（毫秒）")
    token_count: Optional[int] = Field(None, description="token数量")
    task_id: Optional[str] = Field(None, description="任务ID")
    agent_run_id: Optional[str] = Field(None, description="代理运行ID")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class MessageResponse(BaseModel):
    """消息响应模型"""
    id: str = Field(..., description="消息ID")
    conversation_id: str = Field(..., description="对话ID")

    # 消息内容
    content: str = Field(..., description="消息内容")
    message_type: MessageType = Field(..., description="消息类型")
    status: MessageStatus = Field(..., description="消息状态")

    # 元数据
    metadata: dict[str, Any] = Field(..., description="元数据")
    attachments: list[dict[str, Any]] = Field(..., description="附件")

    # 处理信息
    processing_time: Optional[int] = Field(None, description="处理时间（毫秒）")
    token_count: int = Field(..., description="token数量")

    # 任务关联
    task_id: Optional[str] = Field(None, description="任务ID")
    agent_run_id: Optional[str] = Field(None, description="代理运行ID")

    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")

    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    # 标记
    is_edited: bool = Field(..., description="是否已编辑")

    class Config:
        from_attributes = True

    @classmethod
    def from_document(cls, doc: Message) -> "MessageResponse":
        """从 MongoDB 文档创建响应模型"""
        return cls(
            id=str(doc.id),
            conversation_id=str(doc.conversation_id),
            content=doc.content,
            message_type=doc.message_type,
            status=doc.status,
            metadata=doc.metadata,
            attachments=doc.attachments,
            processing_time=doc.processing_time,
            token_count=doc.token_count,
            task_id=doc.task_id,
            agent_run_id=doc.agent_run_id,
            error_message=doc.error_message,
            error_code=doc.error_code,
            created_at=doc.created_at,
            updated_at=doc.updated_at,
            completed_at=doc.completed_at,
            is_edited=doc.is_edited,
        )


class MessageList(BaseModel):
    """消息列表响应模型"""
    messages: list[MessageResponse] = Field(..., description="消息列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class MessageFilter(BaseModel):
    """消息过滤条件"""
    conversation_id: str = Field(..., description="对话ID")
    message_type: Optional[MessageType] = Field(None, description="消息类型过滤")
    status: Optional[MessageStatus] = Field(None, description="状态过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=50, ge=1, le=200, description="每页大小")
    
    # 排序参数
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="asc", regex="^(asc|desc)$", description="排序方向")


class ConversationHistory(BaseModel):
    """对话历史模型"""
    conversation_id: str = Field(..., description="对话ID")
    messages: list[MessageResponse] = Field(..., description="消息历史")
    total_messages: int = Field(..., description="总消息数")
    total_tokens: int = Field(..., description="总token数")
    created_at: datetime = Field(..., description="对话创建时间")
    last_activity_at: datetime = Field(..., description="最后活动时间")
