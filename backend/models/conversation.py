"""
对话线程模型
Conversation Thread Models
"""

from datetime import datetime
from enum import Enum
from typing import Any, Optional

from beanie import Document
from pydantic import BaseModel, Field
from pymongo import IndexModel


class ConversationStatus(str, Enum):
    """对话状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class ConversationThread(Document):
    """对话线程 MongoDB 文档模型"""

    # 基本信息
    title: str = Field(..., min_length=1, max_length=255, description="对话标题")
    description: Optional[str] = Field(None, max_length=1000, description="对话描述")
    status: ConversationStatus = Field(default=ConversationStatus.ACTIVE, description="对话状态")

    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")

    # 配置信息
    character_id: str = Field(default="default", description="角色ID")
    context: dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    settings: dict[str, Any] = Field(default_factory=dict, description="设置信息")

    # 统计信息
    message_count: int = Field(default=0, description="消息数量")
    total_tokens: int = Field(default=0, description="总token数")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    last_activity_at: datetime = Field(default_factory=datetime.utcnow, description="最后活动时间")

    # 标记
    is_deleted: bool = Field(default=False, description="是否已删除")
    is_pinned: bool = Field(default=False, description="是否置顶")

    class Settings:
        name = "conversation_threads"
        indexes = [
            IndexModel([("user_id", 1), ("created_at", -1)]),
            IndexModel([("status", 1)]),
            IndexModel([("last_activity_at", -1)]),
            IndexModel([("is_deleted", 1)]),
            IndexModel([("character_id", 1)]),
            IndexModel([("title", "text"), ("description", "text")]),  # 文本搜索索引
        ]

    def update_activity(self):
        """更新活动时间"""
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def update_stats(self, message_count_delta: int = 0, token_count_delta: int = 0):
        """更新统计信息"""
        self.message_count += message_count_delta
        self.total_tokens += token_count_delta
        self.update_activity()


class ConversationThreadCreate(BaseModel):
    """创建对话线程请求模型"""
    title: str = Field(..., min_length=1, max_length=255, description="对话标题")
    description: Optional[str] = Field(None, max_length=1000, description="对话描述")
    character_id: str = Field(default="default", description="角色ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    settings: dict[str, Any] = Field(default_factory=dict, description="设置信息")


class ConversationThreadUpdate(BaseModel):
    """更新对话线程请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="对话标题")
    description: Optional[str] = Field(None, max_length=1000, description="对话描述")
    status: Optional[ConversationStatus] = Field(None, description="对话状态")
    character_id: Optional[str] = Field(None, description="角色ID")
    context: Optional[dict[str, Any]] = Field(None, description="上下文信息")
    settings: Optional[dict[str, Any]] = Field(None, description="设置信息")
    is_pinned: Optional[bool] = Field(None, description="是否置顶")


class ConversationThreadResponse(BaseModel):
    """对话线程响应模型"""
    id: str = Field(..., description="对话ID")
    title: str = Field(..., description="对话标题")
    description: Optional[str] = Field(None, description="对话描述")
    status: ConversationStatus = Field(..., description="对话状态")

    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")

    # 配置信息
    character_id: str = Field(..., description="角色ID")
    context: dict[str, Any] = Field(..., description="上下文信息")
    settings: dict[str, Any] = Field(..., description="设置信息")

    # 统计信息
    message_count: int = Field(..., description="消息数量")
    total_tokens: int = Field(..., description="总token数")

    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_activity_at: datetime = Field(..., description="最后活动时间")

    # 标记
    is_pinned: bool = Field(..., description="是否置顶")

    class Config:
        from_attributes = True

    @classmethod
    def from_document(cls, doc: ConversationThread) -> "ConversationThreadResponse":
        """从 MongoDB 文档创建响应模型"""
        return cls(
            id=str(doc.id),
            title=doc.title,
            description=doc.description,
            status=doc.status,
            user_id=doc.user_id,
            session_id=doc.session_id,
            character_id=doc.character_id,
            context=doc.context,
            settings=doc.settings,
            message_count=doc.message_count,
            total_tokens=doc.total_tokens,
            created_at=doc.created_at,
            updated_at=doc.updated_at,
            last_activity_at=doc.last_activity_at,
            is_pinned=doc.is_pinned,
        )


class ConversationThreadList(BaseModel):
    """对话线程列表响应模型"""
    threads: list[ConversationThreadResponse] = Field(..., description="对话线程列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class ConversationThreadFilter(BaseModel):
    """对话线程过滤条件"""
    status: Optional[ConversationStatus] = Field(None, description="状态过滤")
    user_id: Optional[str] = Field(None, description="用户ID过滤")
    character_id: Optional[str] = Field(None, description="角色ID过滤")
    is_pinned: Optional[bool] = Field(None, description="是否置顶过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    # 排序参数
    sort_by: str = Field(default="last_activity_at", description="排序字段")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="排序方向")
