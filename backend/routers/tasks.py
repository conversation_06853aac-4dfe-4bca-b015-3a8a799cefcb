"""
任务管理 API 路由
Task Management API Routes
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.task import (
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskList,
    TaskFilter,
    TaskStats,
    TaskType,
    TaskStatus,
    TaskPriority,
)
from ..services.task_service import TaskService

# 创建路由器
router = APIRouter(prefix="/tasks", tags=["Tasks"])

# 依赖注入 - 这里需要根据实际的数据库配置来实现
async def get_db_session() -> AsyncSession:
    """获取数据库会话"""
    # TODO: 实现实际的数据库会话获取
    # 这里返回一个模拟的会话
    return None

async def get_task_service(db: AsyncSession = Depends(get_db_session)) -> TaskService:
    """获取任务服务"""
    return TaskService(db)


@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_data: TaskCreate,
    task_service: TaskService = Depends(get_task_service)
):
    """
    创建新任务
    
    - **task_type**: 任务类型
    - **task_name**: 任务名称
    - **description**: 任务描述（可选）
    - **priority**: 任务优先级
    - **conversation_id**: 对话ID（可选）
    - **message_id**: 消息ID（可选）
    - **user_id**: 用户ID（可选）
    - **input_data**: 输入数据
    - **metadata**: 元数据
    - **queue_name**: 队列名称（可选）
    - **max_retries**: 最大重试次数
    - **estimated_duration**: 预估时长（秒）
    """
    try:
        logger.info(f"Creating new task: {task_data.task_name}")
        
        task = await task_service.create_task(task_data)
        
        logger.success(f"Created task: {task.task_id}")
        return task
        
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create task: {str(e)}"
        )


@router.get("/", response_model=TaskList)
async def list_tasks(
    task_type: TaskType = None,
    status_filter: TaskStatus = None,
    priority: TaskPriority = None,
    conversation_id: str = None,
    user_id: str = None,
    is_system_task: bool = None,
    page: int = 1,
    page_size: int = 20,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务列表
    
    - **task_type**: 任务类型过滤
    - **status**: 状态过滤
    - **priority**: 优先级过滤
    - **conversation_id**: 对话ID过滤
    - **user_id**: 用户ID过滤
    - **is_system_task**: 是否系统任务过滤
    - **page**: 页码
    - **page_size**: 每页大小
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向 (asc, desc)
    """
    try:
        filter_params = TaskFilter(
            task_type=task_type,
            status=status_filter,
            priority=priority,
            conversation_id=conversation_id,
            user_id=user_id,
            is_system_task=is_system_task,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
        )
        
        tasks = await task_service.list_tasks(filter_params)
        
        logger.info(f"Listed {len(tasks.tasks)} tasks")
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to list tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list tasks: {str(e)}"
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务详情
    
    - **task_id**: 任务ID
    """
    try:
        task = await task_service.get_task(task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task: {str(e)}"
        )


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: str,
    update_data: TaskUpdate,
    task_service: TaskService = Depends(get_task_service)
):
    """
    更新任务
    
    - **task_id**: 任务ID
    - **status**: 任务状态（可选）
    - **progress**: 进度百分比（可选）
    - **output_data**: 输出数据（可选）
    - **metadata**: 元数据（可选）
    - **worker_id**: 工作节点ID（可选）
    - **started_at**: 开始时间（可选）
    - **completed_at**: 完成时间（可选）
    - **actual_duration**: 实际时长（可选）
    - **error_message**: 错误信息（可选）
    - **error_code**: 错误代码（可选）
    - **error_details**: 错误详情（可选）
    """
    try:
        task = await task_service.update_task(task_id, update_data)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )
        
        logger.info(f"Updated task: {task_id}")
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update task: {str(e)}"
        )


@router.post("/{task_id}/cancel", response_model=Dict[str, Any])
async def cancel_task(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    取消任务
    
    - **task_id**: 任务ID
    """
    try:
        success = await task_service.cancel_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )
        
        logger.info(f"Cancelled task: {task_id}")
        
        return {
            "message": f"Task {task_id} cancelled successfully",
            "task_id": task_id,
            "status": "cancelled"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel task: {str(e)}"
        )


@router.get("/stats/overview", response_model=TaskStats)
async def get_task_stats(
    user_id: str = None,
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务统计
    
    - **user_id**: 用户ID过滤（可选）
    """
    try:
        stats = await task_service.get_task_stats(user_id)
        
        logger.info(f"Retrieved task stats for user: {user_id or 'all'}")
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get task stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task stats: {str(e)}"
        )


@router.post("/animation", response_model=TaskResponse)
async def submit_animation_task(
    request_data: Dict[str, Any],
    task_service: TaskService = Depends(get_task_service)
):
    """
    提交动画生成任务
    
    - **conversation_id**: 对话ID
    - **message_id**: 消息ID
    - **text**: 动画描述文本
    - **character_id**: 角色ID（可选，默认为 "default"）
    - **context**: 上下文信息（可选）
    - **user_id**: 用户ID（可选）
    """
    try:
        # 验证必需参数
        required_fields = ["conversation_id", "message_id", "text"]
        for field in required_fields:
            if field not in request_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        task = await task_service.submit_animation_task(
            conversation_id=request_data["conversation_id"],
            message_id=request_data["message_id"],
            text=request_data["text"],
            character_id=request_data.get("character_id", "default"),
            context=request_data.get("context"),
            user_id=request_data.get("user_id"),
        )
        
        logger.info(f"Submitted animation task: {task.task_id}")
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit animation task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit animation task: {str(e)}"
        )


@router.post("/conversation", response_model=TaskResponse)
async def submit_conversation_task(
    request_data: Dict[str, Any],
    task_service: TaskService = Depends(get_task_service)
):
    """
    提交对话处理任务
    
    - **conversation_id**: 对话ID
    - **message_id**: 消息ID
    - **content**: 消息内容
    - **message_type**: 消息类型（可选，默认为 "user"）
    - **context**: 上下文信息（可选）
    - **user_id**: 用户ID（可选）
    """
    try:
        # 验证必需参数
        required_fields = ["conversation_id", "message_id", "content"]
        for field in required_fields:
            if field not in request_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        task = await task_service.submit_conversation_task(
            conversation_id=request_data["conversation_id"],
            message_id=request_data["message_id"],
            content=request_data["content"],
            message_type=request_data.get("message_type", "user"),
            context=request_data.get("context"),
            user_id=request_data.get("user_id"),
        )
        
        logger.info(f"Submitted conversation task: {task.task_id}")
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit conversation task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit conversation task: {str(e)}"
        )


@router.get("/conversation/{conversation_id}", response_model=TaskList)
async def get_conversation_tasks(
    conversation_id: str,
    status_filter: TaskStatus = None,
    task_type: TaskType = None,
    page: int = 1,
    page_size: int = 20,
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取对话相关的任务列表
    
    - **conversation_id**: 对话ID
    - **status**: 状态过滤（可选）
    - **task_type**: 任务类型过滤（可选）
    - **page**: 页码
    - **page_size**: 每页大小
    """
    try:
        filter_params = TaskFilter(
            conversation_id=conversation_id,
            status=status_filter,
            task_type=task_type,
            page=page,
            page_size=page_size,
            sort_by="created_at",
            sort_order="desc",
        )
        
        tasks = await task_service.list_tasks(filter_params)
        
        logger.info(f"Listed {len(tasks.tasks)} tasks for conversation {conversation_id}")
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to get tasks for conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation tasks: {str(e)}"
        )
