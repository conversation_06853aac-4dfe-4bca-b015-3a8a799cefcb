"""
动作数据结构定义
Data models for motion generation system
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class ActionType(str, Enum):
    """Types of actions that can be performed"""
    LOCOMOTION = "locomotion"      # Walking, running, jumping
    GESTURE = "gesture"            # Hand waves, pointing, etc.
    POSE = "pose"                  # Standing, sitting, crouching
    INTERACTION = "interaction"    # Picking up objects, pushing, etc.
    EXPRESSION = "expression"      # Facial expressions
    CUSTOM = "custom"              # Custom defined actions


class Action(BaseModel):
    """Individual action within a motion sequence"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: ActionType
    name: str
    duration: float = Field(default=1.0, description="Duration in seconds")
    start_time: float = Field(default=0.0, description="Start time in sequence")
    parameters: dict[str, Any] = Field(default_factory=dict)

    class Config:
        use_enum_values = True


class ActionSequence(BaseModel):
    """Sequence of actions that form a complete motion"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    actions: list[Action]
    total_duration: float
    character_id: str = "default"
    created_at: datetime = Field(default_factory=datetime.now)

    def add_action(self, action: Action) -> None:
        """Add an action to the sequence"""
        if self.actions:
            action.start_time = self.actions[-1].start_time + self.actions[-1].duration
        else:
            action.start_time = 0.0

        self.actions.append(action)
        self.total_duration = action.start_time + action.duration


class MotionRequest(BaseModel):
    """Request for motion generation from natural language"""
    text: str = Field(..., description="Natural language description of the motion")
    character_id: str = Field(default="default", description="ID of the character to animate")
    context: dict[str, Any] | None = Field(default=None, description="Additional context")

    class Config:
        schema_extra = {
            "example": {
                "text": "Walk forward slowly and then wave your right hand",
                "character_id": "character_01",
                "context": {"environment": "indoor", "mood": "happy"}
            }
        }


class MotionResponse(BaseModel):
    """Response containing the generated motion data"""
    success: bool
    action_sequence: ActionSequence | None = None
    original_text: str = ""
    processed_text: str = ""
    error_message: str | None = None
    blender_script_path: str | None = None
    fbx_output_path: str | None = None
    processing_time: float | None = None

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "action_sequence": {
                    "id": "seq_123",
                    "actions": [
                        {
                            "id": "act_1",
                            "type": "locomotion",
                            "name": "walk",
                            "duration": 3.0,
                            "start_time": 0.0,
                            "parameters": {"speed": "slow", "direction": "forward"}
                        }
                    ],
                    "total_duration": 3.0,
                    "character_id": "character_01"
                },
                "original_text": "Walk forward slowly",
                "processed_text": "walk forward slowly",
                "blender_script_path": "blender_scripts/generate_animation.py"
            }
        }


class BlenderAnimationConfig(BaseModel):
    """Configuration for Blender animation generation"""
    character_model_path: str
    output_fbx_path: str
    frame_rate: int = Field(default=24, description="Animation frame rate")
    quality: str = Field(default="medium", description="Animation quality: low, medium, high")
    export_format: str = Field(default="fbx", description="Export format: fbx, gltf, etc.")

    class Config:
        schema_extra = {
            "example": {
                "character_model_path": "/models/character.blend",
                "output_fbx_path": "/output/animation.fbx",
                "frame_rate": 30,
                "quality": "high",
                "export_format": "fbx"
            }
        }
