"""
专业游戏动画师模块
Professional Game Animator Module
"""

from .animator_functions import IntermediateAnimator, JuniorAnimator, UnifiedAnimator
from .api import router as animation_router
from .models import (
    AnimationIntensity,
    AnimationRequest,
    AnimationResponse,
    AnimationSequence,
    AnimationType,
    AnimatorAction,
    BlenderExportConfig,
    BodyPart,
    Direction,
    MotionCaptureData,
)
from .professional_nlu import ProfessionalAnimatorNLU
from .professional_pipeline import ProfessionalAnimationPipeline

__all__ = [
    # 数据模型
    "AnimationType",
    "BodyPart",
    "Direction",
    "AnimationIntensity",
    "AnimatorAction",
    "AnimationSequence",
    "MotionCaptureData",
    "AnimationRequest",
    "AnimationResponse",
    "BlenderExportConfig",

    # 核心组件
    "ProfessionalAnimatorNLU",
    "UnifiedAnimator",
    "JuniorAnimator",  # Legacy compatibility
    "IntermediateAnimator",  # Legacy compatibility
    "ProfessionalAnimationPipeline",

    # API路由
    "animation_router"
]
