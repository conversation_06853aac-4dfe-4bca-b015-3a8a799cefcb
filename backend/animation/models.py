"""
游戏动画师专业数据模型
Professional Game Animator Data Models
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class AnimationType(str, Enum):
    """游戏动画类型分类"""

    # 基础移动
    LOCOMOTION = "locomotion"  # 移动类：走、跑、跳
    IDLE = "idle"  # 待机类：呼吸、等待

    # 战斗动画
    COMBAT_ATTACK = "combat_attack"  # 攻击动画
    COMBAT_DEFEND = "combat_defend"  # 防御动画
    COMBAT_HIT = "combat_hit"  # 受击动画
    COMBAT_DEATH = "combat_death"  # 死亡动画

    # 特技动作
    ACROBATIC = "acrobatic"  # 特技类：翻滚、后空翻
    PARKOUR = "parkour"  # 跑酷类：攀爬、跳跃

    # 交互动画
    INTERACTION = "interaction"  # 交互类：开门、拾取
    GESTURE = "gesture"  # 手势类：挥手、指向
    EMOTE = "emote"  # 表情类：微笑、愤怒

    # 过渡动画
    TRANSITION = "transition"  # 过渡动画
    BLEND = "blend"  # 融合动画


class BodyPart(str, Enum):
    """身体部位枚举"""

    FULL_BODY = "full_body"
    UPPER_BODY = "upper_body"
    LOWER_BODY = "lower_body"

    HEAD = "head"
    NECK = "neck"

    LEFT_ARM = "left_arm"
    RIGHT_ARM = "right_arm"
    LEFT_HAND = "left_hand"
    RIGHT_HAND = "right_hand"

    TORSO = "torso"
    SPINE = "spine"

    LEFT_LEG = "left_leg"
    RIGHT_LEG = "right_leg"
    LEFT_FOOT = "left_foot"
    RIGHT_FOOT = "right_foot"

    HIPS = "hips"


class Direction(str, Enum):
    """方向枚举"""

    FORWARD = "forward"
    BACKWARD = "backward"
    LEFT = "left"
    RIGHT = "right"
    UP = "up"
    DOWN = "down"

    # 对角线方向
    FORWARD_LEFT = "forward_left"
    FORWARD_RIGHT = "forward_right"
    BACKWARD_LEFT = "backward_left"
    BACKWARD_RIGHT = "backward_right"


class AnimationIntensity(str, Enum):
    """动画强度"""

    VERY_SLOW = "very_slow"
    SLOW = "slow"
    NORMAL = "normal"
    FAST = "fast"
    VERY_FAST = "very_fast"
    EXPLOSIVE = "explosive"


class AnimatorAction(BaseModel):
    """动画师专业动作定义"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))

    # 基础属性
    type: AnimationType = Field(..., description="动画类型")
    name: str = Field(..., description="动作名称")

    # 专业参数
    direction: Direction | None = Field(None, description="移动方向")
    steps: int | None = Field(None, description="步数（适用于移动类动作）")
    angle: float | None = Field(None, description="旋转角度（度）")
    distance: float | None = Field(None, description="移动距离（米）")

    # 身体部位
    primary_body_parts: list[BodyPart] = Field(
        default_factory=list, description="主要涉及的身体部位"
    )
    secondary_body_parts: list[BodyPart] = Field(
        default_factory=list, description="次要涉及的身体部位"
    )

    # 动画属性
    duration: float = Field(default=1.0, description="持续时间（秒）")
    intensity: AnimationIntensity = Field(
        default=AnimationIntensity.NORMAL, description="动画强度"
    )
    is_looping: bool = Field(default=False, description="是否循环播放")

    # 时间轴
    start_time: float = Field(default=0.0, description="开始时间")
    end_time: float | None = Field(None, description="结束时间")

    # 关键帧数据
    keyframes: dict[str, Any] = Field(default_factory=dict, description="关键帧数据")

    # 动画曲线
    easing: str = Field(default="linear", description="缓动曲线类型")

    # 专业参数
    root_motion: bool = Field(default=True, description="是否包含根运动")
    in_place: bool = Field(default=False, description="是否原地动画")

    # 战斗相关
    hit_frame: int | None = Field(None, description="打击帧（战斗动画）")
    recovery_frame: int | None = Field(None, description="恢复帧")

    # 附加数据
    metadata: dict[str, Any] = Field(default_factory=dict, description="附加元数据")


class AnimationSequence(BaseModel):
    """动画序列"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., description="序列名称")
    actions: list[AnimatorAction] = Field(..., description="动作列表")

    # 序列属性
    total_duration: float = Field(..., description="总持续时间")
    frame_rate: int = Field(default=30, description="帧率")
    total_frames: int = Field(..., description="总帧数")

    # 角色信息
    character_id: str = Field(default="default", description="角色ID")
    character_rig: str | None = Field(None, description="角色骨骼绑定")

    # 质量设置
    quality_level: str = Field(default="medium", description="质量等级")
    compression: str = Field(default="none", description="压缩设置")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = Field(None)

    # 导出设置
    export_settings: dict[str, Any] = Field(default_factory=dict)


class MotionCaptureData(BaseModel):
    """动捕数据模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source_file: str = Field(..., description="源文件路径")

    # 清理状态
    is_cleaned: bool = Field(default=False, description="是否已清理")
    noise_level: float = Field(default=0.0, description="噪声水平")

    # 数据质量
    quality_score: float = Field(default=0.0, description="质量评分")
    missing_frames: list[int] = Field(default_factory=list, description="缺失帧")

    # 处理记录
    processing_log: list[str] = Field(default_factory=list, description="处理日志")


class AnimationRequest(BaseModel):
    """动画生成请求"""

    text: str = Field(..., description="自然语言描述")
    character_id: str = Field(default="default", description="角色ID")

    # 专业设置
    quality_target: str = Field(default="game_ready", description="质量目标")

    # 技术要求
    frame_rate: int = Field(default=30, description="目标帧率")
    export_format: str = Field(default="fbx", description="导出格式")

    # 上下文
    context: dict[str, Any] | None = Field(None, description="上下文信息")
    reference_animations: list[str] = Field(
        default_factory=list, description="参考动画"
    )


class AnimationResponse(BaseModel):
    """动画生成响应"""

    success: bool = Field(..., description="是否成功")

    # 结果数据
    animation_sequence: AnimationSequence | None = Field(
        None, description="生成的动画序列"
    )
    fbx_file_path: str | None = Field(None, description="FBX文件路径")

    # 处理信息
    original_text: str = Field(..., description="原始文本")
    processed_actions: list[str] = Field(
        default_factory=list, description="处理的动作列表"
    )

    # 质量报告
    quality_report: dict[str, Any] = Field(default_factory=dict, description="质量报告")

    # 错误信息
    error_message: str | None = Field(None, description="错误信息")
    warnings: list[str] = Field(default_factory=list, description="警告信息")

    # 性能数据
    processing_time: float | None = Field(None, description="处理时间")
    memory_usage: float | None = Field(None, description="内存使用")


class BlenderExportConfig(BaseModel):
    """Blender导出配置"""

    # 文件设置
    output_path: str = Field(..., description="输出路径")
    file_format: str = Field(default="fbx", description="文件格式")

    # FBX设置
    fbx_version: str = Field(default="7.4", description="FBX版本")
    scale_factor: float = Field(default=1.0, description="缩放因子")

    # 动画设置
    bake_animation: bool = Field(default=True, description="烘焙动画")
    frame_start: int = Field(default=1, description="起始帧")
    frame_end: int = Field(default=250, description="结束帧")

    # 优化设置
    optimize_keyframes: bool = Field(default=True, description="优化关键帧")
    remove_redundant: bool = Field(default=True, description="移除冗余数据")

    # 质量设置
    sampling_rate: float = Field(default=1.0, description="采样率")
    compression_level: str = Field(default="medium", description="压缩级别")
