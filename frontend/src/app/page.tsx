'use client';

import { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import MotionGenerator from '@/components/MotionGenerator';
import AnimationPreview from '@/components/AnimationPreview';
import ExampleRequests from '@/components/ExampleRequests';
import { AnimationResponse, HealthStatus } from '@/types/motion';
import { checkHealth } from '@/lib/api';
import { Activity, AlertCircle, CheckCircle } from 'lucide-react';

export default function Home() {
  const [response, setResponse] = useState<AnimationResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExample, setSelectedExample] = useState<string>('');
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [healthData, setHealthData] = useState<HealthStatus | null>(null);

  // Check backend health on component mount
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const health = await checkHealth();
        setHealthData(health);
        setBackendStatus('online');
      } catch (error) {
        console.error('Backend health check failed:', error);
        setBackendStatus('offline');
      }
    };

    checkBackendHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkBackendHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleExampleSelect = (text: string) => {
    setSelectedExample(text);
    // Clear previous results when selecting new example
    setResponse(null);
    setError(null);
  };

  const StatusIndicator = () => (
    <div className="flex items-center gap-2 text-sm">
      {backendStatus === 'checking' && (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Checking backend...</span>
        </>
      )}
      {backendStatus === 'online' && (
        <>
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span className="text-green-600">Backend Online</span>
          {healthData && (
            <span className="text-gray-500">v{healthData.version}</span>
          )}
        </>
      )}
      {backendStatus === 'offline' && (
        <>
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-red-600">Backend Offline</span>
        </>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Activity className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Motion Agent</h1>
                <p className="text-sm text-gray-600">Professional 3D Animation Generator</p>
              </div>
            </div>
            <StatusIndicator />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Input and Examples */}
          <div className="space-y-8">
            <MotionGenerator
              onResponse={setResponse}
              onLoading={setLoading}
              onError={setError}
              selectedExample={selectedExample}
            />

            <ExampleRequests onSelectExample={handleExampleSelect} />
          </div>

          {/* Right Column - Preview */}
          <div>
            <AnimationPreview
              response={response}
              loading={loading}
              error={error}
            />
          </div>
        </div>

        {/* Backend Status Details */}
        {healthData && backendStatus === 'online' && (
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Backend Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600">NLU Pipeline</div>
                <div className="font-semibold text-green-700">
                  {healthData.nlu_pipeline || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600">LangGraph</div>
                <div className="font-semibold text-blue-700">
                  {healthData.langgraph_pipeline || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-sm text-gray-600">Professional Animator</div>
                <div className="font-semibold text-purple-700">
                  {healthData.professional_animator || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-sm text-gray-600">Version</div>
                <div className="font-semibold text-orange-700">{healthData.version}</div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>Motion Agent - Professional 3D Animation Generator</p>
            <p className="text-sm mt-2">
              Powered by MotionGPT + Motion Agent + MoDi + Blender + Python
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
