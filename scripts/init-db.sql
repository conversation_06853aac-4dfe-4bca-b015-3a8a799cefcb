// Motion Agent MongoDB 数据库初始化脚本
// Motion Agent MongoDB Database Initialization Script

// 切换到 motion_agent 数据库
db = db.getSiblingDB('motion_agent');

// 创建用户（如果需要）
// db.createUser({
//   user: "motion_agent_user",
//   pwd: "motion_agent_password",
//   roles: [
//     { role: "readWrite", db: "motion_agent" }
//   ]
// });

// 创建集合和索引
print('Creating collections and indexes...');

// 对话线程集合
db.createCollection('conversation_threads');
db.conversation_threads.createIndex({ "user_id": 1, "created_at": -1 });
db.conversation_threads.createIndex({ "status": 1 });
db.conversation_threads.createIndex({ "last_activity_at": -1 });
db.conversation_threads.createIndex({ "is_deleted": 1 });
db.conversation_threads.createIndex({ "character_id": 1 });
db.conversation_threads.createIndex({
  "title": "text",
  "description": "text"
}, {
  name: "text_search_index"
});

// 消息集合
db.createCollection('messages');
db.messages.createIndex({ "conversation_id": 1, "created_at": 1 });
db.messages.createIndex({ "message_type": 1 });
db.messages.createIndex({ "status": 1 });
db.messages.createIndex({ "task_id": 1 });
db.messages.createIndex({ "is_deleted": 1 });
db.messages.createIndex({ "content": "text" }, { name: "message_text_search" });

// 任务集合
db.createCollection('tasks');
db.tasks.createIndex({ "task_id": 1 }, { unique: true });
db.tasks.createIndex({ "conversation_id": 1 });
db.tasks.createIndex({ "status": 1, "created_at": -1 });
db.tasks.createIndex({ "task_type": 1 });
db.tasks.createIndex({ "user_id": 1 });
db.tasks.createIndex({ "priority": 1, "created_at": 1 });
db.tasks.createIndex({ "queue_name": 1 });
db.tasks.createIndex({ "is_system_task": 1 });

// 插入示例数据（可选）
print('Inserting sample data...');

// 示例对话线程
db.conversation_threads.insertOne({
  title: "示例对话",
  description: "这是一个示例对话线程",
  status: "active",
  user_id: "demo_user",
  character_id: "default",
  context: { demo: true },
  settings: {},
  message_count: 0,
  total_tokens: 0,
  created_at: new Date(),
  updated_at: new Date(),
  last_activity_at: new Date(),
  is_deleted: false,
  is_pinned: false
});

print('Motion Agent MongoDB database initialized successfully');
print('Collections created: conversation_threads, messages, tasks');
print('Indexes created for optimal query performance');
