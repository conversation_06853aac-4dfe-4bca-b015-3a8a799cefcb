# Motion Agent MongoDB + Taskiq 框架完整指南

## 🎯 概述

本项目已完全重构为基于 **MongoDB** 和 **Taskiq** 的现代化架构，实现了：

- ✅ **MongoDB** 作为主数据库，替代 PostgreSQL
- ✅ **完全配置化** 的系统，所有端口和设置都可通过配置文件管理
- ✅ **Beanie ODM** 用于 MongoDB 文档建模
- ✅ **对话线程管理** - 每个对话都是独立的 thread
- ✅ **异步任务处理** - 基于 Taskiq + Redis
- ✅ **统一配置管理** - 所有可配置项都在配置文件中

## 🏗️ 新架构特点

### 1. 配置驱动架构
- 所有端口、数据库连接、队列设置都通过 `.env` 文件配置
- 支持多环境配置（开发、测试、生产）
- 配置验证和自动创建必要目录

### 2. MongoDB 数据存储
- 使用 MongoDB 替代 PostgreSQL
- Beanie ODM 提供类型安全的文档操作
- 自动索引创建和优化查询性能

### 3. 完全异步处理
- 所有数据库操作都是异步的
- Taskiq 任务队列处理长时间运行的任务
- 支持任务状态跟踪和错误处理

## 📁 新增文件结构

```
motion-agent/
├── backend/
│   ├── config.py                      # 🆕 统一配置管理系统
│   ├── database.py                    # 🔄 MongoDB 数据库配置
│   ├── models/                        # 🔄 基于 Beanie 的数据模型
│   │   ├── conversation.py           # MongoDB 对话模型
│   │   ├── message.py                # MongoDB 消息模型
│   │   └── task.py                   # MongoDB 任务模型
│   ├── tasks/                         # 🔄 更新的 Taskiq 任务
│   ├── services/                      # 🔄 更新的服务层
│   └── routers/                       # 🔄 更新的 API 路由
├── scripts/
│   └── init-mongodb.js               # 🆕 MongoDB 初始化脚本
├── start_backend_config.py           # 🆕 配置化启动脚本
├── docker-compose.dev.yml            # 🔄 更新为 MongoDB
├── .env.example                      # 🔄 完整的配置示例
└── MONGODB_TASKIQ_GUIDE.md          # 🆕 本指南
```

## ⚙️ 配置系统

### 配置文件结构

所有配置都在 `backend/config.py` 中定义，包括：

- **数据库配置** (`DatabaseConfig`)
- **Redis 配置** (`RedisConfig`) 
- **API 配置** (`APIConfig`)
- **Taskiq 配置** (`TaskiqConfig`)
- **Blender 配置** (`BlenderConfig`)
- **日志配置** (`LoggingConfig`)
- **安全配置** (`SecurityConfig`)
- **开发配置** (`DevelopmentConfig`)

### 环境变量示例

```env
# 应用配置
APP_NAME=Motion Agent
API_HOST=0.0.0.0
API_PORT=9000

# MongoDB 配置
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=motion_agent
MONGODB_MAX_CONNECTIONS=100

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Taskiq 配置
TASKIQ_WORKER_CONCURRENCY=4
TASKIQ_MAX_RETRIES=3
TASKIQ_ANIMATION_TIMEOUT=600

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_ROTATION=10 MB
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
uv sync

# 复制配置文件
cp .env.example .env

# 编辑配置（可选）
vim .env
```

### 2. 启动服务

```bash
# 启动基础设施（MongoDB + Redis）
python start_dev.py

# 启动 API 服务器（使用配置）
python start_backend_config.py

# 启动任务处理器
python start_worker.py
```

### 3. 验证系统

```bash
# 运行完整测试
python test_framework.py

# 检查健康状态
curl http://localhost:9000/health
```

## 🗄️ MongoDB 数据模型

### 对话线程 (ConversationThread)

```python
class ConversationThread(Document):
    title: str
    description: Optional[str]
    status: ConversationStatus
    user_id: Optional[str]
    character_id: str = "default"
    context: dict[str, Any] = {}
    message_count: int = 0
    total_tokens: int = 0
    created_at: datetime
    updated_at: datetime
    last_activity_at: datetime
    is_deleted: bool = False
    is_pinned: bool = False
```

### 消息 (Message)

```python
class Message(Document):
    conversation_id: ObjectId
    content: str
    message_type: MessageType
    status: MessageStatus
    metadata: dict[str, Any] = {}
    attachments: list[dict[str, Any]] = []
    processing_time: Optional[int]
    token_count: int = 0
    task_id: Optional[str]
    created_at: datetime
    completed_at: Optional[datetime]
```

### 任务 (Task)

```python
class Task(Document):
    task_id: str  # taskiq 任务ID
    task_type: TaskType
    task_name: str
    status: TaskStatus
    progress: float = 0.0
    conversation_id: Optional[ObjectId]
    input_data: dict[str, Any] = {}
    output_data: dict[str, Any] = {}
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
```

## 🔄 任务队列系统

### 队列配置

```python
# 队列名称都可配置
TASKIQ_DEFAULT_QUEUE=default
TASKIQ_ANIMATION_QUEUE=animation
TASKIQ_CONVERSATION_QUEUE=conversation
TASKIQ_NLU_QUEUE=nlu
TASKIQ_SYSTEM_QUEUE=system
```

### 任务类型

1. **动画生成任务** (`animation_generation`)
2. **对话处理任务** (`conversation_processing`)
3. **NLU 处理任务** (`nlu_processing`)
4. **系统维护任务** (`system_maintenance`)

## 🌐 API 端点

### 对话管理

```bash
# 创建对话
POST /conversations/
{
  "title": "动画生成对话",
  "character_id": "default"
}

# 发送消息
POST /conversations/{id}/messages
{
  "content": "请创建一个行走动画",
  "message_type": "user"
}

# 开始动画生成
POST /conversations/{id}/animation
{
  "text": "角色向前走5步",
  "character_id": "default"
}
```

### 任务管理

```bash
# 获取任务状态
GET /tasks/{task_id}

# 获取任务列表
GET /tasks/?status=running&page=1

# 取消任务
POST /tasks/{task_id}/cancel

# 获取统计信息
GET /tasks/stats/overview
```

## 🔧 开发工具

### 管理界面

- **MongoDB Express**: http://localhost:8080 (admin/admin)
- **Redis Commander**: http://localhost:8081
- **API 文档**: http://localhost:9000/docs

### 配置验证

```python
from backend.config import validate_config

# 验证所有配置
if validate_config():
    print("配置验证通过")
```

### 日志配置

```python
# 所有日志设置都可配置
LOG_LEVEL=INFO
LOG_FORMAT_CONSOLE=<green>{time}</green> | <level>{level}</level> | {message}
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
```

## 🐳 Docker 支持

### 开发环境

```bash
# 启动 MongoDB + Redis
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

### 服务地址

- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **MongoDB Express**: http://localhost:8080
- **Redis Commander**: http://localhost:8081

## 📊 监控和调试

### 健康检查

```bash
curl http://localhost:9000/health
```

返回详细的系统状态：

```json
{
  "status": "healthy",
  "version": "3.0.0",
  "services": {
    "database": "healthy",
    "taskiq": "ready"
  },
  "database": {
    "type": "mongodb",
    "collections": 3,
    "connections": 5
  }
}
```

### 任务统计

```bash
curl http://localhost:9000/tasks/stats/overview
```

### 日志查看

```bash
# API 服务器日志
tail -f logs/motion_agent.log

# Worker 日志
tail -f logs/worker.log
```

## 🔒 安全配置

### 生产环境设置

```env
# 安全配置
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com
ENABLE_API_KEY_AUTH=true
VALID_API_KEYS=key1,key2,key3

# 数据库安全
MONGODB_USERNAME=motion_agent_user
MONGODB_PASSWORD=secure_password
REDIS_PASSWORD=redis_password
```

## 🚀 部署建议

### 生产环境

1. **使用环境变量**管理敏感配置
2. **启用认证**和访问控制
3. **配置 HTTPS**和防火墙
4. **设置监控**和告警
5. **定期备份**数据库

### 性能优化

1. **调整连接池**大小
2. **优化 MongoDB 索引**
3. **配置 Redis 内存**策略
4. **调整 Worker 并发**数

## 🆕 迁移指南

### 从 PostgreSQL 迁移

如果您之前使用的是 PostgreSQL 版本：

1. **备份现有数据**
2. **更新依赖**: `uv sync`
3. **更新配置**: 复制新的 `.env.example`
4. **启动新服务**: `python start_dev.py`
5. **迁移数据**: 编写迁移脚本（如需要）

### 配置迁移

旧的硬编码配置现在都可以通过环境变量设置：

```bash
# 旧方式：硬编码
API_PORT = 9000

# 新方式：配置化
API_PORT=9000  # 在 .env 文件中
```

## 🎉 总结

新的 MongoDB + Taskiq 框架提供了：

- ✅ **更好的可扩展性** - MongoDB 的灵活性
- ✅ **完全配置化** - 所有设置都可配置
- ✅ **更强的类型安全** - Beanie ODM
- ✅ **更好的开发体验** - 统一的配置管理
- ✅ **生产就绪** - 完整的监控和安全特性

现在您可以轻松地：
- 🔧 **调整任何端口或设置**
- 📊 **监控系统状态**
- 🚀 **部署到任何环境**
- 🔄 **扩展新功能**

开始使用新框架，享受更好的开发体验！ 🎊
