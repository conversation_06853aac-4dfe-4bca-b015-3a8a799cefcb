# Motion Agent Taskiq 框架使用指南

## 概述

本项目已集成 Taskiq 任务队列框架，实现了基于对话线程的异步任务处理系统。每个对话都是一个独立的 thread，支持保存对话历史和异步任务处理。

## 架构设计

### 核心组件

1. **对话线程管理 (Conversation Threads)**
   - 每个对话都是一个独立的线程
   - 支持对话历史保存
   - 支持对话状态管理

2. **任务队列系统 (Task Queue)**
   - 基于 Taskiq + Redis 实现
   - 支持多种任务类型
   - 支持任务状态跟踪和重试

3. **数据持久化 (Data Persistence)**
   - 使用 PostgreSQL 存储对话和任务数据
   - 支持异步数据库操作
   - 支持数据库迁移

### 数据模型

- **ConversationThread**: 对话线程模型
- **Message**: 消息模型
- **Task**: 任务模型

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
uv sync

# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 2. 启动服务

#### 启动数据库和 Redis

```bash
# 启动 PostgreSQL (使用 Docker)
docker run -d \
  --name motion-agent-postgres \
  -e POSTGRES_DB=motion_agent \
  -e POSTGRES_USER=user \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15

# 启动 Redis (使用 Docker)
docker run -d \
  --name motion-agent-redis \
  -p 6379:6379 \
  redis:7-alpine
```

#### 启动 API 服务器

```bash
# 启动 FastAPI 服务器
python start_backend.py
```

#### 启动 Taskiq Worker

```bash
# 启动任务处理器
python start_worker.py
```

### 3. API 使用示例

#### 创建对话线程

```bash
curl -X POST "http://localhost:9000/conversations/" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "动画生成对话",
    "description": "创建角色行走动画",
    "character_id": "default",
    "context": {"scene": "outdoor"}
  }'
```

#### 发送消息

```bash
curl -X POST "http://localhost:9000/conversations/{conversation_id}/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "请创建一个角色向前走5步的动画",
    "message_type": "user"
  }'
```

#### 开始动画生成

```bash
curl -X POST "http://localhost:9000/conversations/{conversation_id}/animation" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "角色向前走5步",
    "character_id": "default",
    "context": {"style": "realistic"}
  }'
```

#### 查看任务状态

```bash
curl "http://localhost:9000/tasks/{task_id}"
```

## API 端点

### 对话管理

- `POST /conversations/` - 创建对话线程
- `GET /conversations/` - 获取对话列表
- `GET /conversations/{id}` - 获取对话详情
- `PUT /conversations/{id}` - 更新对话
- `DELETE /conversations/{id}` - 删除对话
- `GET /conversations/{id}/history` - 获取对话历史
- `POST /conversations/{id}/messages` - 发送消息
- `POST /conversations/{id}/animation` - 开始动画生成

### 任务管理

- `POST /tasks/` - 创建任务
- `GET /tasks/` - 获取任务列表
- `GET /tasks/{id}` - 获取任务详情
- `PUT /tasks/{id}` - 更新任务
- `POST /tasks/{id}/cancel` - 取消任务
- `GET /tasks/stats/overview` - 获取任务统计
- `POST /tasks/animation` - 提交动画生成任务
- `POST /tasks/conversation` - 提交对话处理任务

## 任务类型

### 1. 动画生成任务 (animation_generation)

处理自然语言到动画的转换：

```python
# 任务流程
1. NLU 处理 - 解析自然语言
2. 动作序列生成 - 生成动作序列
3. Blender 动画生成 - 生成 3D 动画
4. FBX 导出 - 导出动画文件
```

### 2. 对话处理任务 (conversation_processing)

处理用户消息和生成回复：

```python
# 任务流程
1. 加载对话历史
2. 处理用户消息
3. 生成回复
4. 保存消息
5. 更新对话统计
```

## 配置说明

### 数据库配置

```env
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/motion_agent
DATABASE_URL_SYNC=postgresql://user:password@localhost:5432/motion_agent
```

### Redis 配置

```env
REDIS_URL=redis://localhost:6379/0
REDIS_RESULT_URL=redis://localhost:6379/1
```

### 任务队列配置

```env
TASKIQ_WORKER_CONCURRENCY=4
TASKIQ_MAX_RETRIES=3
TASKIQ_RETRY_DELAY=60
```

## 开发指南

### 添加新任务类型

1. 在 `backend/tasks/` 目录下创建新的任务文件
2. 使用 `@broker.task()` 装饰器定义任务
3. 在 `backend/models/task.py` 中添加新的任务类型枚举
4. 在 `backend/services/task_service.py` 中添加提交方法

### 扩展数据模型

1. 在 `backend/models/` 目录下修改或添加模型
2. 创建数据库迁移文件
3. 更新服务层代码

### 自定义队列

在 `backend/tasks/__init__.py` 中添加新的队列名称：

```python
class QueueNames:
    CUSTOM_QUEUE = "custom_queue"
```

## 监控和调试

### 查看日志

```bash
# API 服务器日志
tail -f logs/motion_agent.log

# Worker 日志
tail -f logs/worker.log
```

### 健康检查

```bash
curl "http://localhost:9000/health"
```

### 任务统计

```bash
curl "http://localhost:9000/tasks/stats/overview"
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 是否运行
   - 验证数据库配置

2. **Redis 连接失败**
   - 检查 Redis 是否运行
   - 验证 Redis 配置

3. **任务处理失败**
   - 检查 Worker 是否运行
   - 查看任务错误日志

### 重置系统

```bash
# 清理数据库
docker exec motion-agent-postgres psql -U user -d motion_agent -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"

# 清理 Redis
docker exec motion-agent-redis redis-cli FLUSHALL

# 重启服务
python start_backend.py
python start_worker.py
```

## 性能优化

### 数据库优化

- 使用连接池
- 添加适当的索引
- 定期清理过期数据

### 任务队列优化

- 调整 Worker 并发数
- 使用不同优先级队列
- 实现任务批处理

### 缓存策略

- 缓存频繁查询的数据
- 使用 Redis 作为缓存层
- 实现智能缓存失效

## 部署建议

### 生产环境

1. 使用 Docker Compose 部署
2. 配置负载均衡
3. 设置监控和告警
4. 实现自动扩缩容

### 安全考虑

1. 使用 HTTPS
2. 配置防火墙
3. 实现身份认证
4. 定期备份数据
