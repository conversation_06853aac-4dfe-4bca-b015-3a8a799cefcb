import bpy


class FormatConverter:
    def __init__(self):
        self.supported_formats = {
            "fbx": self._export_fbx,
            "obj": self._export_obj,
            "gltf": self._export_gltf,
        }

    def convert(self, input_file: str, output_format: str, output_file: str) -> bool:
        if output_format not in self.supported_formats:
            return False

        try:
            # 导入文件
            bpy.ops.import_scene.fbx(filepath=input_file)

            # 导出为指定格式
            return self.supported_formats[output_format](output_file)
        except Exception as e:
            print(f"Conversion failed: {str(e)}")
            return False

    def _export_fbx(self, filepath: str) -> bool:
        bpy.ops.export_scene.fbx(filepath=filepath)
        return True

    def _export_obj(self, filepath: str) -> bool:
        bpy.ops.export_scene.obj(filepath=filepath)
        return True

    def _export_gltf(self, filepath: str) -> bool:
        bpy.ops.export_scene.gltf(filepath=filepath)
        return True
