from typing import Any

import bpy


class CameraController:
    def __init__(self):
        self.camera = bpy.data.cameras.new("MainCamera")
        self.camera_obj = bpy.data.objects.new("Camera", self.camera)
        bpy.context.scene.collection.objects.link(self.camera_obj)

    def set_camera_position(self, position: dict[str, float]):
        self.camera_obj.location = (position["x"], position["y"], position["z"])

    def set_camera_rotation(self, rotation: dict[str, float]):
        self.camera_obj.rotation_euler = (rotation["x"], rotation["y"], rotation["z"])

    def create_camera_animation(self, keyframes: list[dict[str, Any]]):
        for frame, data in enumerate(keyframes):
            self.camera_obj.location = (
                data["position"]["x"],
                data["position"]["y"],
                data["position"]["z"],
            )
            self.camera_obj.rotation_euler = (
                data["rotation"]["x"],
                data["rotation"]["y"],
                data["rotation"]["z"],
            )
            self.camera_obj.keyframe_insert(data_path="location", frame=frame)
            self.camera_obj.keyframe_insert(data_path="rotation_euler", frame=frame)
