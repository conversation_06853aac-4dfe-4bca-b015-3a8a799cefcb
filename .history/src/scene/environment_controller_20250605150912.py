from typing import Any

import bpy


class EnvironmentController:
    def __init__(self):
        self.particle_systems = {}
        self.lights = {}

    def create_particle_system(
        self, name: str, particle_type: str, settings: dict[str, Any]
    ):
        # 创建粒子系统
        obj = bpy.data.objects.new(name, None)
        bpy.context.scene.collection.objects.link(obj)

        # 添加粒子系统
        particle_sys = obj.modifiers.new(name="ParticleSystem", type="PARTICLE_SYSTEM")
        settings = particle_sys.settings

        # 设置粒子系统参数
        settings.type = particle_type
        settings.count = settings.get("count", 1000)
        settings.lifetime = settings.get("lifetime", 50)

        self.particle_systems[name] = particle_sys

    def create_light(self, name: str, light_type: str, settings: dict[str, Any]):
        # 创建灯光
        light_data = bpy.data.lights.new(name=name, type=light_type)
        light_obj = bpy.data.objects.new(name=name, object_data=light_data)
        bpy.context.scene.collection.objects.link(light_obj)

        # 设置灯光参数
        light_data.energy = settings.get("energy", 1000)
        light_data.color = settings.get("color", (1, 1, 1))

        self.lights[name] = light_obj
