from typing import Any


class MotionBlender:
    def __init__(self):
        self.blend_factors = {"walk": 0.3, "run": 0.2, "jump": 0.4}

    def blend_motions(
        self, motion1: dict[str, Any], motion2: dict[str, Any], blend_factor: float
    ) -> dict[str, Any]:
        # 实现动作混合逻辑
        blended_motion = {}
        for key in motion1:
            if key in motion2:
                blended_motion[key] = (1 - blend_factor) * motion1[
                    key
                ] + blend_factor * motion2[key]
        return blended_motion

    def create_transition(
        self, from_motion: dict[str, Any], to_motion: dict[str, Any], duration: float
    ) -> list[dict[str, Any]]:
        # 实现动作过渡逻辑
        transition_frames = []
        for i in range(int(duration * 30)):  # 假设30fps
            blend_factor = i / (duration * 30)
            transition_frames.append(
                self.blend_motions(from_motion, to_motion, blend_factor)
            )
        return transition_frames
