#!/usr/bin/env python3
"""
Test script to verify the frame_rate None fix
"""

import asyncio
import os
import sys

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from animation.models import AnimationRequest
from animation.professional_nlu import ProfessionalAnimatorNLU


async def test_frame_rate_none_fix():
    """Test that the fix handles None frame_rate properly"""
    print("Testing frame_rate None fix...")

    # Create NLU instance
    nlu = ProfessionalAnimatorNLU()

    # Create a request with the Chinese text from the error
    test_text = "全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行"

    # Test with normal frame_rate
    print("\n1. Testing with normal frame_rate (30)...")
    request_normal = AnimationRequest(
        text=test_text,
        character_id="test_character",
        frame_rate=30
    )

    try:
        response_normal = await nlu.process_natural_language(request_normal)
        print(f"✅ Normal frame_rate test: Success = {response_normal.success}")
        if response_normal.animation_sequence:
            print(f"   Frame rate: {response_normal.animation_sequence.frame_rate}")
            print(f"   Total frames: {response_normal.animation_sequence.total_frames}")
    except Exception as e:
        print(f"❌ Normal frame_rate test failed: {e}")

    # Test with None frame_rate (simulate the bug condition)
    print("\n2. Testing with None frame_rate...")
    request_none = AnimationRequest(
        text=test_text,
        character_id="test_character",
        frame_rate=None  # This should trigger our fix
    )

    try:
        response_none = await nlu.process_natural_language(request_none)
        print(f"✅ None frame_rate test: Success = {response_none.success}")
        if response_none.animation_sequence:
            print(f"   Frame rate: {response_none.animation_sequence.frame_rate}")
            print(f"   Total frames: {response_none.animation_sequence.total_frames}")
    except Exception as e:
        print(f"❌ None frame_rate test failed: {e}")

    print("\n✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(test_frame_rate_none_fix())
