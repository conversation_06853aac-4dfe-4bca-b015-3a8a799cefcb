#!/usr/bin/env python3
"""
开发环境启动脚本
Development Environment Startup Script
"""

import asyncio
import os
import subprocess
import sys
import time
from pathlib import Path

from loguru import logger


def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO",
    )


def run_command(command: str, cwd: str = None) -> bool:
    """运行命令"""
    try:
        logger.info(f"Running: {command}")
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.success(f"Command completed successfully")
            if result.stdout:
                logger.debug(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"Command failed with code {result.returncode}")
            if result.stderr:
                logger.error(f"Error: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run command: {e}")
        return False


def check_docker():
    """检查 Docker 是否可用"""
    logger.info("Checking Docker availability...")
    
    if not run_command("docker --version"):
        logger.error("Docker is not available. Please install Docker first.")
        return False
    
    if not run_command("docker-compose --version"):
        logger.error("Docker Compose is not available. Please install Docker Compose first.")
        return False
    
    logger.success("Docker is available")
    return True


def start_infrastructure():
    """启动基础设施服务"""
    logger.info("Starting infrastructure services...")
    
    # 停止可能存在的容器
    run_command("docker-compose -f docker-compose.dev.yml down")
    
    # 启动服务
    if not run_command("docker-compose -f docker-compose.dev.yml up -d"):
        logger.error("Failed to start infrastructure services")
        return False
    
    logger.success("Infrastructure services started")
    
    # 等待服务启动
    logger.info("Waiting for services to be ready...")
    time.sleep(10)
    
    # 检查服务状态
    if not run_command("docker-compose -f docker-compose.dev.yml ps"):
        logger.warning("Could not check service status")
    
    return True


def check_dependencies():
    """检查 Python 依赖"""
    logger.info("Checking Python dependencies...")
    
    try:
        import fastapi
        import taskiq
        import redis
        import sqlalchemy
        import asyncpg
        logger.success("All required dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        logger.info("Please run: uv sync")
        return False


def create_directories():
    """创建必要的目录"""
    logger.info("Creating necessary directories...")
    
    directories = [
        "logs",
        "output/animations",
        "temp/animation_data",
        "scripts",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.debug(f"Created directory: {directory}")
    
    logger.success("Directories created")


def setup_environment():
    """设置环境"""
    logger.info("Setting up environment...")
    
    # 检查 .env 文件
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            logger.info("Copying .env.example to .env")
            run_command("cp .env.example .env")
        else:
            logger.warning(".env.example not found, please create .env manually")
    
    logger.success("Environment setup completed")


async def wait_for_services():
    """等待服务就绪"""
    logger.info("Waiting for services to be ready...")
    
    import httpx
    
    # 等待 PostgreSQL
    max_retries = 30
    for i in range(max_retries):
        try:
            # 这里应该测试数据库连接，暂时跳过
            break
        except Exception:
            if i < max_retries - 1:
                logger.info(f"Waiting for PostgreSQL... ({i+1}/{max_retries})")
                await asyncio.sleep(2)
            else:
                logger.warning("PostgreSQL may not be ready")
    
    # 等待 Redis
    for i in range(max_retries):
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            logger.success("Redis is ready")
            break
        except Exception:
            if i < max_retries - 1:
                logger.info(f"Waiting for Redis... ({i+1}/{max_retries})")
                await asyncio.sleep(2)
            else:
                logger.warning("Redis may not be ready")


def show_info():
    """显示服务信息"""
    logger.info("\n" + "="*60)
    logger.info("🚀 Motion Agent Development Environment with MongoDB")
    logger.info("="*60)
    logger.info("Services:")
    logger.info("  📊 API Server: http://localhost:9000")
    logger.info("  📊 API Docs: http://localhost:9000/docs")
    logger.info("  🗄️  MongoDB: localhost:27017")
    logger.info("  🔴 Redis: localhost:6379")
    logger.info("  🔧 MongoDB Express: http://localhost:8080 (admin / admin)")
    logger.info("  🔧 Redis Commander: http://localhost:8081")
    logger.info("\nNext steps:")
    logger.info("  1. Start API server: python start_backend.py")
    logger.info("  2. Start worker: python start_worker.py")
    logger.info("  3. Run tests: python test_framework.py")
    logger.info("\nConfiguration:")
    logger.info("  📋 Edit .env file to customize settings")
    logger.info("  🔧 All ports and services are configurable")
    logger.info("="*60)


async def main():
    """主函数"""
    setup_logging()
    
    logger.info("🚀 Starting Motion Agent Development Environment")
    
    try:
        # 检查 Docker
        if not check_docker():
            sys.exit(1)
        
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        # 创建目录
        create_directories()
        
        # 设置环境
        setup_environment()
        
        # 启动基础设施
        if not start_infrastructure():
            sys.exit(1)
        
        # 等待服务就绪
        await wait_for_services()
        
        # 显示信息
        show_info()
        
        logger.success("✅ Development environment is ready!")
        
    except KeyboardInterrupt:
        logger.info("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
