#!/usr/bin/env python3
"""
Taskiq 框架测试脚本
Taskiq Framework Test Script
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from loguru import logger

# 导入配置
from backend.config import get_config


class FrameworkTester:
    """框架测试器"""

    def __init__(self, base_url: str = None):
        # 使用配置中的API地址
        if base_url is None:
            config = get_config()
            base_url = f"http://{config.api.api_host}:{config.api.api_port}"

        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.conversation_id = None
        self.task_id = None
    
    async def test_health_check(self):
        """测试健康检查"""
        logger.info("Testing health check...")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            health_data = response.json()
            logger.success(f"Health check passed: {health_data['status']}")
            
            # 显示服务状态
            services = health_data.get("services", {})
            for service, status in services.items():
                logger.info(f"  {service}: {status}")
            
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def test_create_conversation(self):
        """测试创建对话"""
        logger.info("Testing conversation creation...")
        
        try:
            conversation_data = {
                "title": "测试对话",
                "description": "这是一个测试对话",
                "character_id": "default",
                "context": {"test": True}
            }
            
            response = await self.client.post(
                f"{self.base_url}/conversations/",
                json=conversation_data
            )
            response.raise_for_status()
            
            conversation = response.json()
            self.conversation_id = conversation["id"]
            
            logger.success(f"Conversation created: {self.conversation_id}")
            logger.info(f"  Title: {conversation['title']}")
            logger.info(f"  Status: {conversation['status']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create conversation: {e}")
            return False
    
    async def test_list_conversations(self):
        """测试获取对话列表"""
        logger.info("Testing conversation listing...")
        
        try:
            response = await self.client.get(f"{self.base_url}/conversations/")
            response.raise_for_status()
            
            conversations = response.json()
            logger.success(f"Found {conversations['total']} conversations")
            
            for conv in conversations["threads"]:
                logger.info(f"  - {conv['title']} ({conv['id'][:8]}...)")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to list conversations: {e}")
            return False
    
    async def test_send_message(self):
        """测试发送消息"""
        if not self.conversation_id:
            logger.warning("No conversation ID available, skipping message test")
            return False
        
        logger.info("Testing message sending...")
        
        try:
            message_data = {
                "conversation_id": self.conversation_id,
                "content": "请创建一个角色向前走的动画",
                "message_type": "user"
            }
            
            response = await self.client.post(
                f"{self.base_url}/conversations/{self.conversation_id}/messages",
                json=message_data
            )
            response.raise_for_status()
            
            result = response.json()
            self.task_id = result.get("task_id")
            
            logger.success(f"Message sent successfully")
            logger.info(f"  Task ID: {self.task_id}")
            logger.info(f"  Status: {result['status']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def test_animation_generation(self):
        """测试动画生成"""
        if not self.conversation_id:
            logger.warning("No conversation ID available, skipping animation test")
            return False
        
        logger.info("Testing animation generation...")
        
        try:
            animation_data = {
                "text": "角色向前走5步然后转身",
                "character_id": "default",
                "context": {"style": "realistic"}
            }
            
            response = await self.client.post(
                f"{self.base_url}/conversations/{self.conversation_id}/animation",
                json=animation_data
            )
            response.raise_for_status()
            
            result = response.json()
            animation_task_id = result.get("task_id")
            
            logger.success(f"Animation generation started")
            logger.info(f"  Task ID: {animation_task_id}")
            logger.info(f"  Status: {result['status']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start animation generation: {e}")
            return False
    
    async def test_task_status(self):
        """测试任务状态查询"""
        if not self.task_id:
            logger.warning("No task ID available, skipping task status test")
            return False
        
        logger.info("Testing task status query...")
        
        try:
            response = await self.client.get(f"{self.base_url}/tasks/{self.task_id}")
            response.raise_for_status()
            
            task = response.json()
            logger.success(f"Task status retrieved")
            logger.info(f"  Task ID: {task['task_id']}")
            logger.info(f"  Status: {task['status']}")
            logger.info(f"  Progress: {task['progress']}%")
            logger.info(f"  Type: {task['task_type']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            return False
    
    async def test_task_stats(self):
        """测试任务统计"""
        logger.info("Testing task statistics...")
        
        try:
            response = await self.client.get(f"{self.base_url}/tasks/stats/overview")
            response.raise_for_status()
            
            stats = response.json()
            logger.success(f"Task statistics retrieved")
            logger.info(f"  Total tasks: {stats['total_tasks']}")
            logger.info(f"  Pending: {stats['pending_tasks']}")
            logger.info(f"  Running: {stats['running_tasks']}")
            logger.info(f"  Completed: {stats['completed_tasks']}")
            logger.info(f"  Failed: {stats['failed_tasks']}")
            logger.info(f"  Success rate: {stats['success_rate']:.1f}%")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to get task statistics: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("Starting framework tests...")
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Create Conversation", self.test_create_conversation),
            ("List Conversations", self.test_list_conversations),
            ("Send Message", self.test_send_message),
            ("Animation Generation", self.test_animation_generation),
            ("Task Status", self.test_task_status),
            ("Task Statistics", self.test_task_stats),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if result:
                    logger.success(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} ERROR: {e}")
                results[test_name] = False
            
            # 等待一下再进行下一个测试
            await asyncio.sleep(1)
        
        # 显示测试结果摘要
        logger.info(f"\n{'='*50}")
        logger.info("TEST RESULTS SUMMARY")
        logger.info(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.success("🎉 All tests passed! Framework is working correctly.")
        else:
            logger.warning(f"⚠️  {total - passed} tests failed. Please check the logs.")
        
        return passed == total
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO",
    )
    
    tester = FrameworkTester()
    
    try:
        success = await tester.run_all_tests()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)
        
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
