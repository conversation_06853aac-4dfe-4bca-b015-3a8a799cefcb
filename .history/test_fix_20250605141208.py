#!/usr/bin/env python3
"""
Test script to verify the frame_rate None fix
"""

import asyncio
import os
import sys

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from animation.models import AnimationRequest
from animation.professional_nlu import ProfessionalAnimatorNLU


async def test_frame_rate_none_fix():
    """Test that the fix handles None frame_rate properly"""
    print("Testing frame_rate None fix...")

    # Create NLU instance
    nlu = ProfessionalAnimatorNLU()

    # Create a request with the Chinese text from the error
    test_text = "全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行"

    # Test with normal frame_rate
    print("\n1. Testing with normal frame_rate (30)...")
    request_normal = AnimationRequest(
        text=test_text,
        character_id="test_character",
        frame_rate=30
    )

    try:
        response_normal = await nlu.process_natural_language(request_normal)
        print(f"✅ Normal frame_rate test: Success = {response_normal.success}")
        if response_normal.animation_sequence:
            print(f"   Frame rate: {response_normal.animation_sequence.frame_rate}")
            print(f"   Total frames: {response_normal.animation_sequence.total_frames}")
    except Exception as e:
        print(f"❌ Normal frame_rate test failed: {e}")

    # Test with None frame_rate by directly calling _create_animation_sequence
    print("\n2. Testing internal None frame_rate handling...")
    try:
        # Create a mock action with None end_time to test our fix
        from animation.models import AnimationIntensity, AnimationType, AnimatorAction

        mock_action = AnimatorAction(
            type=AnimationType.IDLE,
            name="test_action",
            duration=2.0,
            intensity=AnimationIntensity.NORMAL,
            end_time=None  # This was causing the original error
        )

        # Test the internal method directly
        sequence = nlu._create_animation_sequence(
            actions=[mock_action],
            character_id="test_character",
            frame_rate=None  # This should trigger our None handling
        )

        print("✅ Internal None frame_rate test: Success")
        print(f"   Frame rate: {sequence.frame_rate}")
        print(f"   Total frames: {sequence.total_frames}")
        print(f"   Total duration: {sequence.total_duration}")

    except Exception as e:
        print(f"❌ Internal None frame_rate test failed: {e}")

    print("\n✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(test_frame_rate_none_fix())
