[project]
name = "motion-agent"
version = "0.1.0"
description = "Natural Language to 3D Animation Generation System"
readme = "README.md"
requires-python = ">=3.11"
authors = [{ name = "BigFaceMaster", email = "<EMAIL>" }]
dependencies = [
  # FastAPI and web framework
  "fastapi>=0.104.1",
  "uvicorn[standard]>=0.24.0",
  "pydantic>=2.5.0",
  "python-multipart>=0.0.6",
  "python-dotenv>=1.0.0",
  "aiofiles>=23.2.1",
  "jinja2>=3.1.2",
  "requests>=2.31.0",
  # Logging
  "loguru>=0.7.2",
  # NLU and AI dependencies
  "spacy[zh]>=3.7.0",
  "transformers>=4.35.0",
  "torch>=2.1.0",
  "numpy>=1.24.0",
  "langchain>=0.0.350",
  "langgraph>=0.0.40",
  # SpaCy models
  "zh_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/zh_core_web_sm-3.5.0/zh_core_web_sm-3.5.0.tar.gz",
  # Animation and math
  "scipy>=1.11.0",
  "matplotlib>=3.7.0",
  "nltk>=3.9.1",
  "haystack-ai>=1.0.0",
]

[project.optional-dependencies]
blender = ["bpy>=4.0.0"]

[dependency-groups]
dev = [
  "black>=23.11.0",
  "flake8>=6.1.0",
  "mypy>=1.7.1",
  "pre-commit>=3.5.0",
  "ruff>=0.1.8",
]

[project.scripts]
start-backend = "start_backend:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["backend"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
mypy_path = "stubs"

# Ignore missing imports for Blender modules when not in Blender environment
[[tool.mypy.overrides]]
module = ["bpy", "bmesh", "mathutils"]
ignore_missing_imports = true


[tool.ruff]
# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8+
target-version = "py38"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = [
  "E4",  # pycodestyle errors
  "E7",  # pycodestyle errors
  "E9",  # pycodestyle errors
  "F",   # Pyflakes
  "W",   # pycodestyle warnings
  "I",   # isort
  "N",   # pep8-naming
  "UP",  # pyupgrade
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "SIM", # flake8-simplify
  "TCH", # flake8-type-checking
]
ignore = [
  "E501", # Line too long (handled by Black)
  "B008", # Do not perform function calls in argument defaults
  "B904", # Allow `raise` without `from` inside `except`
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
