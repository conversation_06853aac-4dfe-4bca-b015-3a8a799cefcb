"""
专业游戏动画师Blender脚本
Professional Game Animator Blender Script
支持初级和中级动画师功能：动捕清理、走跑跳、打击感、特技动作
"""

# Standard library imports
import argparse
import json
import math
import os
import sys
from typing import Any

# Handle numpy import
try:
    import numpy as np
except ImportError:
    print("Warning: numpy not available")
    np = None

# Blender-specific imports - only available in Blender environment
BLENDER_AVAILABLE = False
try:
    import bmesh  # type: ignore
    import bpy  # type: ignore
    import mathutils  # type: ignore
    from mathutils import Euler, Quaternion, Vector  # type: ignore

    BLENDER_AVAILABLE = True
except ImportError:
    print(
        "Warning: Blender modules not available. This script must be run within Blender."
    )
    # Create mock objects for type checking
    bpy = None
    bmesh = None
    mathutils = None
    Vector = None
    Euler = None
    Quaternion = None

# Note: Using string literals for type annotations to avoid runtime issues with Blender types


def require_blender():
    """Ensure Blender is available before running Blender-specific code"""
    if not BLENDER_AVAILABLE:
        raise RuntimeError(
            "This script requires Blender modules. "
            "Please run this script within Blender's Python environment using:\n"
            "blender --background --python blender_scripts/generate_animation.py -- --input data.json --output animation.fbx"
        )


class ProfessionalBlenderAnimator:
    """
    专业游戏动画师Blender类
    Professional Game Animator Blender Class
    """

    def __init__(self):
        require_blender()

        self.scene = bpy.context.scene
        self.frame_rate = 30  # 游戏标准帧率
        self.current_frame = 1

        # 清理场景
        self.clear_scene()

        # 专业动画预设
        self.animation_presets = self._load_professional_presets()

        # 动画师工具
        self.junior_animator = JuniorAnimatorTools()
        self.intermediate_animator = IntermediateAnimatorTools()

        print("Professional Blender Animator initialized")

    def clear_scene(self):
        """Clear all mesh objects from the scene"""
        bpy.ops.object.select_all(action="SELECT")
        bpy.ops.object.delete(use_global=False, confirm=False)

        # Keep camera and lights
        bpy.ops.object.camera_add(location=(7, -7, 5))
        bpy.ops.object.light_add(type="SUN", location=(5, 5, 10))

    def _load_professional_presets(self) -> dict[str, dict[str, Any]]:
        """Load predefined animation presets"""
        return {
            "walk": {
                "keyframes": [
                    {"frame": 0, "location": (0, 0, 0), "rotation": (0, 0, 0)},
                    {"frame": 12, "location": (0, 1, 0), "rotation": (0, 0, 0)},
                    {"frame": 24, "location": (0, 2, 0), "rotation": (0, 0, 0)},
                ],
                "bone_animations": {
                    "leg.L": [
                        {"frame": 0, "rotation": (0, 0, 0)},
                        {"frame": 6, "rotation": (0.5, 0, 0)},
                        {"frame": 12, "rotation": (0, 0, 0)},
                        {"frame": 18, "rotation": (-0.5, 0, 0)},
                        {"frame": 24, "rotation": (0, 0, 0)},
                    ],
                    "leg.R": [
                        {"frame": 0, "rotation": (0, 0, 0)},
                        {"frame": 6, "rotation": (-0.5, 0, 0)},
                        {"frame": 12, "rotation": (0, 0, 0)},
                        {"frame": 18, "rotation": (0.5, 0, 0)},
                        {"frame": 24, "rotation": (0, 0, 0)},
                    ],
                },
            },
            "wave": {
                "bone_animations": {
                    "arm.R": [
                        {"frame": 0, "rotation": (0, 0, 0)},
                        {"frame": 6, "rotation": (0, 0, 1.5)},
                        {"frame": 12, "rotation": (0, 0, 1.2)},
                        {"frame": 18, "rotation": (0, 0, 1.5)},
                        {"frame": 24, "rotation": (0, 0, 0)},
                    ],
                    "hand.R": [
                        {"frame": 0, "rotation": (0, 0, 0)},
                        {"frame": 6, "rotation": (0, 0.3, 0)},
                        {"frame": 12, "rotation": (0, -0.3, 0)},
                        {"frame": 18, "rotation": (0, 0.3, 0)},
                        {"frame": 24, "rotation": (0, 0, 0)},
                    ],
                }
            },
            "jump": {
                "keyframes": [
                    {"frame": 0, "location": (0, 0, 0), "rotation": (0, 0, 0)},
                    {"frame": 6, "location": (0, 0, 0.5), "rotation": (0, 0, 0)},
                    {"frame": 12, "location": (0, 0, 2), "rotation": (0, 0, 0)},
                    {"frame": 18, "location": (0, 0, 0.5), "rotation": (0, 0, 0)},
                    {"frame": 24, "location": (0, 0, 0), "rotation": (0, 0, 0)},
                ]
            },
            "sit": {
                "keyframes": [
                    {"frame": 0, "location": (0, 0, 0), "rotation": (0, 0, 0)},
                    {"frame": 24, "location": (0, 0, -1), "rotation": (0, 0, 0)},
                ],
                "bone_animations": {
                    "spine": [
                        {"frame": 0, "rotation": (0, 0, 0)},
                        {"frame": 24, "rotation": (0.3, 0, 0)},
                    ]
                },
            },
        }

    def load_character_model(
        self, model_path: str
    ) -> Any | None:  # Returns bpy.types.Object
        """Load character model from file"""
        try:
            if model_path.endswith(".blend"):
                # Load from .blend file
                with bpy.data.libraries.load(model_path) as (data_from, data_to):
                    data_to.objects = data_from.objects

                # Link objects to scene
                for obj in data_to.objects:
                    if obj is not None:
                        bpy.context.collection.objects.link(obj)
                        if obj.type == "ARMATURE":
                            return obj

            elif model_path.endswith((".fbx", ".obj")):
                # Import FBX or OBJ
                if model_path.endswith(".fbx"):
                    bpy.ops.import_scene.fbx(filepath=model_path)
                else:
                    bpy.ops.import_scene.obj(filepath=model_path)

                # Find armature object
                for obj in bpy.context.scene.objects:
                    if obj.type == "ARMATURE":
                        return obj

            return None

        except Exception as e:
            print(f"Error loading character model: {e}")
            return None

    def create_default_character(self) -> Any:  # Returns bpy.types.Object
        """Create a simple default character if no model is provided"""
        # Create a simple humanoid figure using basic shapes
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
        body = bpy.context.active_object
        body.name = "Body"

        # Add armature
        bpy.ops.object.armature_add(location=(0, 0, 0))
        armature = bpy.context.active_object
        armature.name = "Character_Armature"

        return armature

    def setup_scene_environment(self, scene_type, environment):
        """根据场景类型和环境特效自动布置场景"""
        # 场景类型：全景/特写/中景/远景
        if scene_type == "panoramic":
            self.scene.camera.location = (0, -20, 10)
            self.scene.camera.rotation_euler = (1.1, 0, 0)
        elif scene_type == "closeup":
            self.scene.camera.location = (0, -2, 1.5)
            self.scene.camera.rotation_euler = (1.5, 0, 0)
        elif scene_type == "medium":
            self.scene.camera.location = (0, -6, 3)
            self.scene.camera.rotation_euler = (1.2, 0, 0)
        elif scene_type == "longshot":
            self.scene.camera.location = (0, -30, 15)
            self.scene.camera.rotation_euler = (1.0, 0, 0)
        # 环境特效
        if environment:
            for env in environment:
                if env == "sandstorm":
                    self.add_sandstorm_effect()
                elif env == "fog":
                    self.add_fog_effect()
                # 可扩展更多环境

    def add_sandstorm_effect(self):
        """添加风沙粒子特效"""
        # 创建粒子系统
        bpy.ops.mesh.primitive_plane_add(size=50, location=(0, 0, 0))
        plane = bpy.context.active_object
        plane.name = "SandstormEmitter"
        ps = plane.modifiers.new("Sandstorm", type="PARTICLE_SYSTEM")
        psys = ps.particle_system
        psys.settings.count = 5000
        psys.settings.lifetime = 100
        psys.settings.frame_start = 1
        psys.settings.frame_end = 200
        psys.settings.normal_factor = 0.5
        psys.settings.physics_type = "NEWTON"
        psys.settings.render_type = "HALO"
        psys.settings.particle_size = 0.1

    def add_fog_effect(self):
        """添加雾效"""
        self.scene.world.mist_settings.use_mist = True
        self.scene.world.mist_settings.start = 5
        self.scene.world.mist_settings.depth = 30

    def animate_camera(self, camera_motion, total_frames):
        """根据镜头运动类型自动生成相机动画"""
        cam = self.scene.camera
        if not cam:
            return
        if camera_motion == "push_in":
            cam.location = (0, -20, 10)
            cam.keyframe_insert(data_path="location", frame=1)
            cam.location = (0, -5, 5)
            cam.keyframe_insert(data_path="location", frame=total_frames)
        elif camera_motion == "tilt_up":
            cam.rotation_euler = (1.5, 0, 0)
            cam.keyframe_insert(data_path="rotation_euler", frame=1)
            cam.rotation_euler = (0.8, 0, 0)
            cam.keyframe_insert(data_path="rotation_euler", frame=total_frames)
        elif camera_motion == "tilt_down":
            cam.rotation_euler = (0.8, 0, 0)
            cam.keyframe_insert(data_path="rotation_euler", frame=1)
            cam.rotation_euler = (1.5, 0, 0)
            cam.keyframe_insert(data_path="rotation_euler", frame=total_frames)
        # 可扩展更多镜头运动

    def add_sound(self, sound_list):
        """在视频序列编辑器中添加音频轨道"""
        if not sound_list:
            return
        # 仅示例，实际需准备音频文件
        for sound in sound_list:
            if sound == "battle_drum":
                sound_path = "public/sounds/battle_drum.wav"
            elif sound == "wind":
                sound_path = "public/sounds/wind.wav"
            elif sound == "sandstorm_wind":
                sound_path = "public/sounds/sandstorm_wind.wav"
            elif sound == "shout":
                sound_path = "public/sounds/shout.wav"
            else:
                continue
            if os.path.exists(sound_path):
                if not self.scene.sequence_editor:
                    self.scene.sequence_editor_create()
                self.scene.sequence_editor.sequences.new_sound(
                    sound, sound_path, channel=1, frame_start=1
                )

    def generate_animation(self, motion_data: dict[str, Any]) -> bool:
        """
        Generate animation from motion data，支持多场景段落
        """
        try:
            scenes = motion_data.get("scenes")
            if scenes and len(scenes) > 1:
                total_frames = 0
                current_frame = 1
                any_action = False
                all_action_names = []
                for seg in scenes:
                    scene_type = seg.get("scene")
                    camera_motion = seg.get("camera_motion")
                    environment = seg.get("environment")
                    sound = seg.get("sound")
                    duration = seg.get("duration")
                    actions = seg.get("actions", [])
                    frames_for_scene = 0
                    for action in actions:
                        any_action = True
                        all_action_names.append(action.get("name"))
                        frames_for_action = int(
                            action.get("duration", 2.0) * self.frame_rate
                        )
                        self.animate_action(
                            self.create_default_character(),
                            action,
                            current_frame,
                            frames_for_action,
                        )
                        current_frame += frames_for_action
                        frames_for_scene += frames_for_action
                    # 场景/环境/相机/音效分段切换
                    self.setup_scene_environment(scene_type, environment)
                    self.animate_camera(camera_motion, frames_for_scene)
                    self.add_sound(sound)
                    total_frames += frames_for_scene
                # 检查是否全为idle
                if not any_action or all(
                    [n.startswith("idle") for n in all_action_names]
                ):
                    print("All actions are idle. Aborting export.")
                    raise RuntimeError(
                        "All actions are idle. No valid animation to export."
                    )
                self.scene.frame_end = total_frames
                return True
            # 单场景兼容
            # 读取场景、镜头、环境、音效参数
            scene_type = motion_data.get("scene")
            camera_motion = motion_data.get("camera_motion")
            environment = motion_data.get("environment")
            sound = motion_data.get("sound")
            # Load or create character
            character_path = motion_data.get("character_model_path")
            if character_path and os.path.exists(character_path):
                character = self.load_character_model(character_path)
            else:
                character = self.create_default_character()
            if not character:
                print("Failed to load or create character")
                raise RuntimeError("Failed to load or create character")
            # Set frame rate
            self.scene.frame_set(1)
            self.scene.frame_start = 1
            # Process action sequence
            action_sequence = motion_data.get("action_sequence", {})
            actions = action_sequence.get("actions", [])
            if not actions:
                print("No valid actions found in single scene. Aborting export.")
                raise RuntimeError("No valid actions found in single scene.")
            all_action_names = [a.get("name") for a in actions]
            if all([n.startswith("idle") for n in all_action_names]):
                print("All actions are idle. Aborting export.")
                raise RuntimeError(
                    "All actions are idle. No valid animation to export."
                )
            total_frames = 0
            current_frame = 1
            for action in actions:
                frames_for_action = int(action.get("duration", 2.0) * self.frame_rate)
                self.animate_action(character, action, current_frame, frames_for_action)
                current_frame += frames_for_action
                total_frames += frames_for_action
            # Set scene end frame
            self.scene.frame_end = total_frames
            # 自动布置场景和环境
            self.setup_scene_environment(scene_type, environment)
            # 自动生成相机动画
            self.animate_camera(camera_motion, total_frames)
            # 添加音效
            self.add_sound(sound)
            return True
        except Exception as e:
            print(f"Error generating animation: {e}")
            import traceback

            traceback.print_exc()
            raise

    def animate_action(
        self,
        character: Any,
        action: dict[str, Any],  # character: bpy.types.Object
        start_frame: int,
        duration_frames: int,
    ):
        """Animate a specific action"""
        action_name = action.get("name", "idle")
        action_type = action.get("type", "pose")
        parameters = action.get("parameters", {})

        # Get animation preset
        preset = self.animation_presets.get(action_name, {})

        if not preset:
            # Create basic idle animation
            self.create_idle_animation(character, start_frame, duration_frames)
            return

        # Apply keyframe animations
        if "keyframes" in preset:
            self.apply_keyframes(
                character, preset["keyframes"], start_frame, duration_frames
            )

        # Apply bone animations if character has armature
        if character.type == "ARMATURE" and "bone_animations" in preset:
            self.apply_bone_animations(
                character,
                preset["bone_animations"],
                start_frame,
                duration_frames,
                parameters,
            )

    def apply_keyframes(
        self,
        obj: Any,
        keyframes: list[dict],  # obj: bpy.types.Object
        start_frame: int,
        duration_frames: int,
    ):
        """Apply keyframe animations to object"""
        for keyframe in keyframes:
            frame = start_frame + int(keyframe["frame"] * duration_frames / 24)

            # Set location
            if "location" in keyframe:
                obj.location = keyframe["location"]
                obj.keyframe_insert(data_path="location", frame=frame)

            # Set rotation
            if "rotation" in keyframe:
                obj.rotation_euler = keyframe["rotation"]
                obj.keyframe_insert(data_path="rotation_euler", frame=frame)

    def apply_bone_animations(
        self,
        armature: Any,
        bone_animations: dict,  # armature: bpy.types.Object
        start_frame: int,
        duration_frames: int,
        parameters: dict,
    ):
        """Apply animations to specific bones"""
        # Enter pose mode
        bpy.context.view_layer.objects.active = armature
        bpy.ops.object.mode_set(mode="POSE")

        for bone_name, keyframes in bone_animations.items():
            if bone_name in armature.pose.bones:
                bone = armature.pose.bones[bone_name]

                for keyframe in keyframes:
                    frame = start_frame + int(keyframe["frame"] * duration_frames / 24)

                    # Apply intensity modifier
                    intensity = parameters.get("intensity", "normal")
                    intensity_multiplier = {
                        "slow": 0.7,
                        "normal": 1.0,
                        "strong": 1.3,
                    }.get(intensity, 1.0)

                    if "rotation" in keyframe:
                        rotation = [
                            r * intensity_multiplier for r in keyframe["rotation"]
                        ]
                        bone.rotation_euler = rotation
                        bone.keyframe_insert(data_path="rotation_euler", frame=frame)

        # Return to object mode
        bpy.ops.object.mode_set(mode="OBJECT")

    def create_idle_animation(
        self, character: Any, start_frame: int, duration_frames: int
    ):  # character: bpy.types.Object
        """Create a basic idle animation"""
        # Simple breathing animation
        for frame in range(start_frame, start_frame + duration_frames, 6):
            scale_factor = 1.0 + 0.02 * (frame % 12 - 6) / 6
            character.scale = (1.0, 1.0, scale_factor)
            character.keyframe_insert(data_path="scale", frame=frame)

    def export_animation(self, output_path: str, format: str = "fbx") -> bool:
        """Export the generated animation"""
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            # Select all objects
            bpy.ops.object.select_all(action="SELECT")
            if format.lower() == "fbx":
                bpy.ops.export_scene.fbx(
                    filepath=output_path,
                    use_selection=True,
                    bake_anim=True,
                    bake_anim_use_all_bones=True,
                    bake_anim_use_nla_strips=False,
                    bake_anim_use_all_actions=False,
                )
            elif format.lower() == "gltf":
                bpy.ops.export_scene.gltf(
                    filepath=output_path, use_selection=True, export_animations=True
                )
            else:
                print(f"Unsupported export format: {format}")
                raise RuntimeError(f"Unsupported export format: {format}")
            if not os.path.exists(output_path):
                print(f"Export failed, file not found: {output_path}")
                raise RuntimeError(f"Export failed, file not found: {output_path}")
            print(f"Animation exported to: {output_path}")
            return True
        except Exception as e:
            print(f"Error exporting animation: {e}")
            import traceback

            traceback.print_exc()
            raise


class JuniorAnimatorTools:
    """初级动画师工具类"""

    def __init__(self):
        self.frame_rate = 30

    def clean_motion_capture_data(self, armature_obj):
        """清理动捕数据"""
        if not armature_obj or armature_obj.type != "ARMATURE":
            return False

        # 选择骨架对象
        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode="POSE")

        # 应用平滑滤波器
        bpy.ops.pose.select_all(action="SELECT")

        # 简化关键帧
        bpy.ops.pose.select_all(action="SELECT")
        for bone in armature_obj.pose.bones:
            bone.bone.select = True

        # 应用噪声减少
        if armature_obj.animation_data and armature_obj.animation_data.action:
            action = armature_obj.animation_data.action
            for fcurve in action.fcurves:
                # 简单的关键帧简化
                bpy.context.scene.frame_set(1)
                fcurve.select = True

        bpy.ops.object.mode_set(mode="OBJECT")
        print("Motion capture data cleaned")
        return True


class IntermediateAnimatorTools:
    """中级动画师工具类"""

    def __init__(self):
        self.frame_rate = 30

    def create_combat_attack_keyframes(
        self, armature_obj, attack_type, start_frame, end_frame
    ):
        """创建战斗攻击关键帧"""
        if not armature_obj:
            return

        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode="POSE")

        duration = end_frame - start_frame
        windup_frame = start_frame + duration // 4
        impact_frame = start_frame + duration // 2
        recovery_frame = end_frame - duration // 4

        if attack_type == "punch":
            self._create_punch_animation(
                armature_obj,
                start_frame,
                windup_frame,
                impact_frame,
                recovery_frame,
                end_frame,
            )
        elif attack_type == "kick":
            self._create_kick_animation(
                armature_obj,
                start_frame,
                windup_frame,
                impact_frame,
                recovery_frame,
                end_frame,
            )

        bpy.ops.object.mode_set(mode="OBJECT")
        print(
            f"Combat attack '{attack_type}' created from frame {start_frame} to {end_frame}"
        )

    def _create_punch_animation(
        self,
        armature_obj,
        start_frame,
        windup_frame,
        impact_frame,
        recovery_frame,
        end_frame,
    ):
        """创建拳击动画"""
        if "arm.R" in armature_obj.pose.bones:
            right_arm = armature_obj.pose.bones["arm.R"]

            # 起始姿态
            bpy.context.scene.frame_set(start_frame)
            right_arm.rotation_euler = (0, 0, 0)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 蓄力
            bpy.context.scene.frame_set(windup_frame)
            right_arm.rotation_euler = (-0.5, 0, -0.3)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 击中
            bpy.context.scene.frame_set(impact_frame)
            right_arm.rotation_euler = (0.3, 0, 0.5)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 恢复
            bpy.context.scene.frame_set(recovery_frame)
            right_arm.rotation_euler = (0.1, 0, 0.2)
            right_arm.keyframe_insert(data_path="rotation_euler")

            # 结束
            bpy.context.scene.frame_set(end_frame)
            right_arm.rotation_euler = (0, 0, 0)
            right_arm.keyframe_insert(data_path="rotation_euler")

    def create_backflip_720_keyframes(self, armature_obj, start_frame, end_frame):
        """创建720度后空翻关键帧"""
        if not armature_obj:
            return

        bpy.context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode="POSE")

        duration = end_frame - start_frame
        quarter_2 = start_frame + duration // 2

        # 根骨骼旋转（如果存在）
        if armature_obj.pose.bones:
            root_bone = armature_obj.pose.bones[0]  # 假设第一个是根骨骼

            # 起始
            bpy.context.scene.frame_set(start_frame)
            root_bone.rotation_euler = (0, 0, 0)
            root_bone.keyframe_insert(data_path="rotation_euler")

            # 第一个360度
            bpy.context.scene.frame_set(quarter_2)
            root_bone.rotation_euler = (math.pi * 2, 0, 0)  # 360度
            root_bone.keyframe_insert(data_path="rotation_euler")

            # 第二个360度
            bpy.context.scene.frame_set(end_frame)
            root_bone.rotation_euler = (math.pi * 4, 0, 0)  # 720度
            root_bone.keyframe_insert(data_path="rotation_euler")

        bpy.ops.object.mode_set(mode="OBJECT")
        print(f"720-degree backflip created from frame {start_frame} to {end_frame}")


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(
        description="Generate Blender animation from motion data"
    )
    parser.add_argument(
        "--input", required=True, help="Input JSON file with motion data"
    )
    parser.add_argument("--output", required=True, help="Output animation file path")
    parser.add_argument("--format", default="fbx", help="Export format (fbx, gltf)")

    # Parse arguments (skip Blender's arguments)
    argv = sys.argv
    if "--" in argv:
        argv = argv[argv.index("--") + 1 :]
    else:
        argv = []

    args = parser.parse_args(argv)

    # Load motion data
    try:
        with open(args.input) as f:
            motion_data = json.load(f)
    except Exception as e:
        print(f"Error loading motion data: {e}")
        import traceback

        traceback.print_exc()
        return

    # 检查输出目录和写权限
    output_dir = os.path.dirname(args.output)
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            print(f"Error creating output directory: {output_dir}, {e}")
            import traceback

            traceback.print_exc()
            return
    if not os.access(output_dir, os.W_OK):
        print(f"No write permission for output directory: {output_dir}")
        return

    # 检查动画数据有效性
    if not motion_data or (
        not motion_data.get("scenes") and not motion_data.get("action_sequence")
    ):
        print("Motion data is empty or invalid. Aborting.")
        return

    generator = ProfessionalBlenderAnimator()

    try:
        animation_generated = generator.generate_animation(motion_data)
    except Exception as e:
        print(f"Exception during animation generation: {e}")
        import traceback

        traceback.print_exc()
        return

    if animation_generated:
        try:
            exported = generator.export_animation(args.output, args.format)
            # 再次确认文件存在且可读
            if (
                exported
                and os.path.exists(args.output)
                and os.access(args.output, os.R_OK)
            ):
                print("Animation generation completed successfully!")
            else:
                print(f"Exported file not found or not readable: {args.output}")
        except Exception as e:
            print(f"Exception during export: {e}")
            import traceback

            traceback.print_exc()
    else:
        print("Failed to generate animation")


if __name__ == "__main__":
    main()
