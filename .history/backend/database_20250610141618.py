"""
MongoDB 数据库配置
MongoDB Database Configuration
"""

from typing import Optional

import motor.motor_asyncio
from beanie import init_beanie
from loguru import logger
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from .config import get_config

# 全局变量
_client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
_database: Optional[motor.motor_asyncio.AsyncIOMotorDatabase] = None


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    
    Yields:
        AsyncSession: 数据库会话
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_database():
    """初始化数据库"""
    try:
        logger.info("Initializing database...")
        
        # 导入所有模型以确保表被创建
        from .models.conversation import ConversationThread
        from .models.message import Message
        from .models.task import Task
        
        # 创建所有表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.success("Database initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def close_database():
    """关闭数据库连接"""
    try:
        logger.info("Closing database connections...")
        await async_engine.dispose()
        logger.success("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")


async def check_database_connection():
    """检查数据库连接"""
    try:
        async with AsyncSessionLocal() as session:
            # 执行简单查询测试连接
            result = await session.execute("SELECT 1")
            result.scalar()
            
        logger.success("Database connection is healthy")
        return True
        
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.async_engine = async_engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """创建新的数据库会话"""
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """健康检查"""
        return await check_database_connection()
    
    async def init(self) -> bool:
        """初始化数据库"""
        return await init_database()
    
    async def close(self):
        """关闭数据库连接"""
        await close_database()


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI 依赖注入函数，用于获取数据库会话
    
    Yields:
        AsyncSession: 数据库会话
    """
    async for session in get_db_session():
        yield session


# 数据库健康检查函数
async def database_health_check() -> dict:
    """
    数据库健康检查
    
    Returns:
        dict: 健康检查结果
    """
    try:
        is_healthy = await check_database_connection()
        
        return {
            "database": {
                "status": "healthy" if is_healthy else "unhealthy",
                "url": DATABASE_URL.split("@")[-1] if "@" in DATABASE_URL else "unknown",
                "engine": "postgresql+asyncpg",
                "pool_size": async_engine.pool.size(),
                "checked_out": async_engine.pool.checkedout(),
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "database": {
                "status": "error",
                "error": str(e),
                "url": "unknown",
                "engine": "postgresql+asyncpg",
            }
        }


# 数据库初始化脚本
async def setup_database():
    """设置数据库（用于启动脚本）"""
    try:
        logger.info("Setting up database...")
        
        # 检查连接
        if not await check_database_connection():
            logger.error("Cannot connect to database")
            return False
        
        # 初始化数据库
        if not await init_database():
            logger.error("Failed to initialize database")
            return False
        
        logger.success("Database setup completed")
        return True
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


# 导出主要组件
__all__ = [
    "async_engine",
    "sync_engine",
    "AsyncSessionLocal",
    "Base",
    "metadata",
    "get_db_session",
    "get_database_session",
    "init_database",
    "close_database",
    "check_database_connection",
    "database_health_check",
    "setup_database",
    "DatabaseManager",
    "db_manager",
]
