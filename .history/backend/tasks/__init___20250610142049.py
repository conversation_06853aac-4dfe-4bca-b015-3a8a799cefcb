"""
Taskiq 配置和任务管理
Taskiq Configuration and Task Management
"""

from typing import Any

from loguru import logger
from taskiq import TaskiqScheduler
from taskiq_redis import ListQueueBroker, RedisAsyncResultBackend

from ..config import get_config

# 获取配置
config = get_config()

# 创建 Redis broker 和 result backend
broker = ListQueueBroker(url=config.redis.redis_url)
result_backend = RedisAsyncResultBackend(redis_url=config.redis.redis_result_url)

# 配置 broker
broker = broker.with_result_backend(result_backend)

# 创建调度器
scheduler = TaskiqScheduler(broker=broker)

# 队列名称常量
class QueueNames:
    """队列名称常量"""
    DEFAULT = config.taskiq.taskiq_default_queue
    ANIMATION = config.taskiq.taskiq_animation_queue
    NLU = config.taskiq.taskiq_nlu_queue
    CONVERSATION = config.taskiq.taskiq_conversation_queue
    SYSTEM = config.taskiq.taskiq_system_queue


def get_task_config(task_type: str) -> dict[str, Any]:
    """根据任务类型获取配置"""
    configs = {
        "animation_generation": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_animation_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.ANIMATION,
        },
        "nlu_processing": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_nlu_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.NLU,
        },
        "conversation_processing": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_conversation_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.CONVERSATION,
        },
        "motion_capture": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_default_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.ANIMATION,
        },
        "blender_export": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_animation_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.ANIMATION,
        },
        "system_maintenance": {
            "max_retries": config.taskiq.taskiq_max_retries,
            "timeout": config.taskiq.taskiq_default_timeout,
            "retry_delay": config.taskiq.taskiq_retry_delay,
            "queue": QueueNames.SYSTEM,
        },
    }

    return configs.get(task_type, {
        "max_retries": config.taskiq.taskiq_max_retries,
        "timeout": config.taskiq.taskiq_default_timeout,
        "retry_delay": config.taskiq.taskiq_retry_delay,
        "queue": QueueNames.DEFAULT,
    })


async def init_taskiq():
    """初始化 taskiq"""
    try:
        logger.info("Initializing taskiq...")
        
        # 测试 Redis 连接
        await broker.startup()
        logger.success("Taskiq initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Failed to initialize taskiq: {e}")
        return False


async def shutdown_taskiq():
    """关闭 taskiq"""
    try:
        logger.info("Shutting down taskiq...")
        await broker.shutdown()
        logger.success("Taskiq shutdown successfully")
    except Exception as e:
        logger.error(f"Error shutting down taskiq: {e}")


# 导出主要组件
__all__ = [
    "broker",
    "result_backend", 
    "scheduler",
    "QueueNames",
    "TaskConfig",
    "get_task_config",
    "init_taskiq",
    "shutdown_taskiq",
]
