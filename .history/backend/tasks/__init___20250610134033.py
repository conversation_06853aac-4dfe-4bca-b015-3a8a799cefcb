"""
Taskiq 配置和任务管理
Taskiq Configuration and Task Management
"""

import os
from typing import Any

from loguru import logger
from taskiq import TaskiqScheduler
from taskiq_redis import ListQueueBroker, RedisAsyncResultBackend

# Redis 配置
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
REDIS_RESULT_URL = os.getenv("REDIS_RESULT_URL", "redis://localhost:6379/1")

# 创建 Redis broker 和 result backend
broker = ListQueueBroker(url=REDIS_URL)
result_backend = RedisAsyncResultBackend(redis_url=REDIS_RESULT_URL)

# 配置 broker
broker = broker.with_result_backend(result_backend)

# 创建调度器
scheduler = TaskiqScheduler(broker=broker)

# 队列名称常量
class QueueNames:
    """队列名称常量"""
    DEFAULT = "default"
    ANIMATION = "animation"
    NLU = "nlu"
    CONVERSATION = "conversation"
    SYSTEM = "system"
    HIGH_PRIORITY = "high_priority"


# 任务配置
class TaskConfig:
    """任务配置"""
    
    # 默认配置
    DEFAULT_MAX_RETRIES = 3
    DEFAULT_RETRY_DELAY = 60  # 秒
    DEFAULT_TIMEOUT = 300  # 5分钟
    
    # 动画生成任务配置
    ANIMATION_MAX_RETRIES = 2
    ANIMATION_TIMEOUT = 600  # 10分钟
    ANIMATION_RETRY_DELAY = 120  # 2分钟
    
    # NLU 处理任务配置
    NLU_MAX_RETRIES = 3
    NLU_TIMEOUT = 60  # 1分钟
    NLU_RETRY_DELAY = 30  # 30秒
    
    # 对话处理任务配置
    CONVERSATION_MAX_RETRIES = 2
    CONVERSATION_TIMEOUT = 180  # 3分钟
    CONVERSATION_RETRY_DELAY = 60  # 1分钟


def get_task_config(task_type: str) -> dict[str, Any]:
    """根据任务类型获取配置"""
    configs = {
        "animation_generation": {
            "max_retries": TaskConfig.ANIMATION_MAX_RETRIES,
            "timeout": TaskConfig.ANIMATION_TIMEOUT,
            "retry_delay": TaskConfig.ANIMATION_RETRY_DELAY,
            "queue": QueueNames.ANIMATION,
        },
        "nlu_processing": {
            "max_retries": TaskConfig.NLU_MAX_RETRIES,
            "timeout": TaskConfig.NLU_TIMEOUT,
            "retry_delay": TaskConfig.NLU_RETRY_DELAY,
            "queue": QueueNames.NLU,
        },
        "conversation_processing": {
            "max_retries": TaskConfig.CONVERSATION_MAX_RETRIES,
            "timeout": TaskConfig.CONVERSATION_TIMEOUT,
            "retry_delay": TaskConfig.CONVERSATION_RETRY_DELAY,
            "queue": QueueNames.CONVERSATION,
        },
        "motion_capture": {
            "max_retries": TaskConfig.DEFAULT_MAX_RETRIES,
            "timeout": TaskConfig.DEFAULT_TIMEOUT,
            "retry_delay": TaskConfig.DEFAULT_RETRY_DELAY,
            "queue": QueueNames.ANIMATION,
        },
        "blender_export": {
            "max_retries": TaskConfig.ANIMATION_MAX_RETRIES,
            "timeout": TaskConfig.ANIMATION_TIMEOUT,
            "retry_delay": TaskConfig.ANIMATION_RETRY_DELAY,
            "queue": QueueNames.ANIMATION,
        },
        "system_maintenance": {
            "max_retries": TaskConfig.DEFAULT_MAX_RETRIES,
            "timeout": TaskConfig.DEFAULT_TIMEOUT,
            "retry_delay": TaskConfig.DEFAULT_RETRY_DELAY,
            "queue": QueueNames.SYSTEM,
        },
    }
    
    return configs.get(task_type, {
        "max_retries": TaskConfig.DEFAULT_MAX_RETRIES,
        "timeout": TaskConfig.DEFAULT_TIMEOUT,
        "retry_delay": TaskConfig.DEFAULT_RETRY_DELAY,
        "queue": QueueNames.DEFAULT,
    })


async def init_taskiq():
    """初始化 taskiq"""
    try:
        logger.info("Initializing taskiq...")
        
        # 测试 Redis 连接
        await broker.startup()
        logger.success("Taskiq initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Failed to initialize taskiq: {e}")
        return False


async def shutdown_taskiq():
    """关闭 taskiq"""
    try:
        logger.info("Shutting down taskiq...")
        await broker.shutdown()
        logger.success("Taskiq shutdown successfully")
    except Exception as e:
        logger.error(f"Error shutting down taskiq: {e}")


# 导出主要组件
__all__ = [
    "broker",
    "result_backend", 
    "scheduler",
    "QueueNames",
    "TaskConfig",
    "get_task_config",
    "init_taskiq",
    "shutdown_taskiq",
]
