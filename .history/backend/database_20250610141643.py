"""
MongoDB 数据库配置
MongoDB Database Configuration
"""

from typing import Optional

import motor.motor_asyncio
from beanie import init_beanie
from loguru import logger
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from .config import get_config

# 全局变量
_client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
_database: Optional[motor.motor_asyncio.AsyncIOMotorDatabase] = None


async def get_database() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    """
    获取数据库实例

    Returns:
        AsyncIOMotorDatabase: MongoDB 数据库实例
    """
    global _database
    if _database is None:
        await init_database()
    return _database


async def get_client() -> motor.motor_asyncio.AsyncIOMotorClient:
    """
    获取 MongoDB 客户端

    Returns:
        AsyncIOMotorClient: MongoDB 客户端实例
    """
    global _client
    if _client is None:
        await init_database()
    return _client


async def init_database():
    """初始化 MongoDB 数据库"""
    global _client, _database

    try:
        logger.info("Initializing MongoDB database...")

        config = get_config()

        # 创建 MongoDB 客户端
        _client = motor.motor_asyncio.AsyncIOMotorClient(
            config.database.mongodb_connection_string,
            maxPoolSize=config.database.mongodb_max_connections,
            minPoolSize=config.database.mongodb_min_connections,
            maxIdleTimeMS=config.database.mongodb_max_idle_time,
            connectTimeoutMS=config.database.mongodb_connect_timeout,
            serverSelectionTimeoutMS=config.database.mongodb_server_selection_timeout,
        )

        # 获取数据库实例
        _database = _client[config.database.mongodb_database]

        # 测试连接
        await _client.admin.command('ping')

        # 导入所有模型
        from .models.conversation import ConversationThread
        from .models.message import Message
        from .models.task import Task

        # 初始化 Beanie
        await init_beanie(
            database=_database,
            document_models=[ConversationThread, Message, Task]
        )

        logger.success(f"MongoDB database initialized successfully: {config.database.mongodb_database}")
        return True

    except Exception as e:
        logger.error(f"Failed to initialize MongoDB database: {e}")
        return False


async def close_database():
    """关闭数据库连接"""
    global _client, _database

    try:
        logger.info("Closing MongoDB connections...")

        if _client:
            _client.close()
            _client = None
            _database = None

        logger.success("MongoDB connections closed")

    except Exception as e:
        logger.error(f"Error closing MongoDB connections: {e}")


async def check_database_connection():
    """检查数据库连接"""
    try:
        config = get_config()

        # 创建临时客户端进行连接测试
        test_client = motor.motor_asyncio.AsyncIOMotorClient(
            config.database.mongodb_connection_string,
            serverSelectionTimeoutMS=5000
        )

        # 执行 ping 命令测试连接
        await test_client.admin.command('ping')
        test_client.close()

        logger.success("MongoDB connection is healthy")
        return True

    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        logger.error(f"MongoDB connection failed: {e}")
        return False
    except Exception as e:
        logger.error(f"MongoDB connection check error: {e}")
        return False


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.async_engine = async_engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """创建新的数据库会话"""
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """健康检查"""
        return await check_database_connection()
    
    async def init(self) -> bool:
        """初始化数据库"""
        return await init_database()
    
    async def close(self):
        """关闭数据库连接"""
        await close_database()


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI 依赖注入函数，用于获取数据库会话
    
    Yields:
        AsyncSession: 数据库会话
    """
    async for session in get_db_session():
        yield session


# 数据库健康检查函数
async def database_health_check() -> dict:
    """
    数据库健康检查
    
    Returns:
        dict: 健康检查结果
    """
    try:
        is_healthy = await check_database_connection()
        
        return {
            "database": {
                "status": "healthy" if is_healthy else "unhealthy",
                "url": DATABASE_URL.split("@")[-1] if "@" in DATABASE_URL else "unknown",
                "engine": "postgresql+asyncpg",
                "pool_size": async_engine.pool.size(),
                "checked_out": async_engine.pool.checkedout(),
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "database": {
                "status": "error",
                "error": str(e),
                "url": "unknown",
                "engine": "postgresql+asyncpg",
            }
        }


# 数据库初始化脚本
async def setup_database():
    """设置数据库（用于启动脚本）"""
    try:
        logger.info("Setting up database...")
        
        # 检查连接
        if not await check_database_connection():
            logger.error("Cannot connect to database")
            return False
        
        # 初始化数据库
        if not await init_database():
            logger.error("Failed to initialize database")
            return False
        
        logger.success("Database setup completed")
        return True
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


# 导出主要组件
__all__ = [
    "async_engine",
    "sync_engine",
    "AsyncSessionLocal",
    "Base",
    "metadata",
    "get_db_session",
    "get_database_session",
    "init_database",
    "close_database",
    "check_database_connection",
    "database_health_check",
    "setup_database",
    "DatabaseManager",
    "db_manager",
]
