"""
专业游戏动画师完整管道
Professional Game Animator Complete Pipeline
集成NLU + 动画师功能 + Blender导出
"""

import json
import os
import subprocess
import time
from typing import Any

from loguru import logger

from .animator_functions import UnifiedAnimator
from .models import (
    AnimationRequest,
    AnimationResponse,
    AnimationSequence,
    AnimatorAction,
)
from .professional_nlu import ProfessionalAnimatorNLU


class ProfessionalAnimationPipeline:
    """
    专业动画生成管道
    完整的从自然语言到FBX文件的流程
    """

    def __init__(self):
        # 初始化组件
        self.nlu = ProfessionalAnimatorNLU()
        self.unified_animator = UnifiedAnimator()

        # 配置
        self.blender_path = self._find_blender_executable()
        self.output_dir = "output/animations"
        self.temp_dir = "temp/animation_data"

        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)

        logger.info("Professional Animation Pipeline initialized")

    async def process_animation_request(
        self, request: AnimationRequest
    ) -> AnimationResponse:
        """
        处理动画生成请求的主要入口点
        """
        start_time = time.time()
        logger.info(f"Processing animation request: {request.text}")

        try:
            # 1. 自然语言理解 - 转换为专业术语
            logger.info("Step 1: Natural Language Understanding")
            nlu_response = await self.nlu.process_natural_language(request)

            if not nlu_response.success:
                return nlu_response

            # 2. 动画师职能处理
            logger.info("Step 2: Animator Function Processing")
            enhanced_sequence = await self._apply_animator_functions(
                nlu_response.animation_sequence
            )

            # 3. 生成Blender数据
            logger.info("Step 3: Generate Blender Animation Data")
            blender_data = self._prepare_blender_data(enhanced_sequence, request)
            blender_data["scene"] = nlu_response.scene
            blender_data["camera_motion"] = nlu_response.camera_motion
            blender_data["environment"] = nlu_response.environment
            blender_data["sound"] = nlu_response.sound

            # 4. 调用Blender生成动画
            logger.info("Step 4: Execute Blender Animation Generation")
            fbx_path = await self._execute_blender_generation(blender_data, request)

            # 5. 生成最终响应
            processing_time = time.time() - start_time

            response = AnimationResponse(
                success=True,
                animation_sequence=enhanced_sequence,
                fbx_file_path=fbx_path,
                original_text=request.text,
                processed_actions=[action.name for action in enhanced_sequence.actions],
                quality_report=self._generate_quality_report(enhanced_sequence),
                processing_time=processing_time,
            )

            logger.success(
                f"Animation generated successfully in {processing_time:.2f}s"
            )
            return response

        except Exception as e:
            logger.error(f"Error in animation pipeline: {e}")
            return AnimationResponse(
                success=False,
                original_text=request.text,
                error_message=str(e),
                processing_time=time.time() - start_time,
            )

    async def _apply_animator_functions(
        self, sequence: AnimationSequence
    ) -> AnimationSequence:
        """
        应用统一动画师职能增强动画序列
        """
        logger.info("Applying unified animator functions")

        enhanced_actions = []

        for action in sequence.actions:
            enhanced_action = await self._enhance_single_action(action)
            enhanced_actions.append(enhanced_action)

        # 添加过渡动画
        if len(enhanced_actions) > 1:
            enhanced_actions = self._add_transitions(enhanced_actions)

        # 重新计算时间轴
        total_duration = 0.0
        for action in enhanced_actions:
            action.start_time = total_duration
            action.end_time = total_duration + action.duration
            total_duration = action.end_time

        # 创建增强后的序列
        enhanced_sequence = AnimationSequence(
            name=f"enhanced_{sequence.name}",
            actions=enhanced_actions,
            total_duration=total_duration,
            frame_rate=sequence.frame_rate,
            total_frames=int(total_duration * sequence.frame_rate),
            character_id=sequence.character_id,
            quality_level="professional",
        )

        return enhanced_sequence

    async def _enhance_single_action(self, action: AnimatorAction) -> AnimatorAction:
        """
        使用统一动画师增强单个动作
        """
        return self._apply_unified_enhancements(action)

    def _apply_unified_enhancements(self, action: AnimatorAction) -> AnimatorAction:
        """
        应用统一动画师增强 - 包含所有级别的功能
        """
        # 基础动作增强
        if action.name == "walk":
            return self.unified_animator.create_walk_cycle(
                direction=action.direction, intensity=action.intensity
            )
        elif action.name == "run":
            return self.unified_animator.create_run_cycle(
                direction=action.direction, intensity=action.intensity
            )
        elif action.name == "jump":
            return self.unified_animator.create_jump_animation(
                direction=action.direction or action.direction.UP,
                height=action.metadata.get("jump_height", 1.0),
            )
        elif action.name.startswith("idle"):
            return self.unified_animator.create_idle_animation(
                style=action.name.replace("idle_", "")
            )
        # 高级动作增强
        elif action.name == "backflip" and action.angle == 720:
            return self.unified_animator.create_backflip_720()
        elif action.name in ["attack", "punch", "kick"]:
            return self.unified_animator.create_combat_attack(
                attack_type=action.name,
                hand=action.primary_body_parts[0]
                if action.primary_body_parts
                else None,
            )
        elif action.name.startswith("turn"):
            return self.unified_animator.create_complex_turn(
                angle=action.angle or 180, steps=action.steps or 0
            )
        elif action.name.startswith("expression"):
            expression_type = action.name.replace("expression_", "")
            return self.unified_animator.create_facial_expression(expression_type)
        else:
            return action

    def _add_transitions(self, actions: list[AnimatorAction]) -> list[AnimatorAction]:
        """
        在动作之间添加过渡
        """
        enhanced_actions = []
        for i, action in enumerate(actions):
            enhanced_actions.append(action)

            # 在动作之间添加过渡（除了最后一个）
            if i < len(actions) - 1:
                transition = self.unified_animator.create_transition(
                    action, actions[i + 1]
                )
                enhanced_actions.append(transition)

        return enhanced_actions

    def _prepare_blender_data(
        self, sequence: AnimationSequence, request: AnimationRequest
    ) -> dict[str, Any]:
        """
        准备Blender动画数据
        """
        blender_data = {
            "sequence": {
                "name": sequence.name,
                "total_duration": sequence.total_duration,
                "frame_rate": sequence.frame_rate,
                "total_frames": sequence.total_frames,
            },
            "actions": [],
            "character": {"id": sequence.character_id, "rig_type": "humanoid"},
            "export_settings": {
                "format": request.export_format,
                "quality": request.quality_target,
                "frame_rate": request.frame_rate,
            },
        }

        # 转换动作数据
        for action in sequence.actions:
            action_data = {
                "name": action.name,
                "type": action.type.value,
                "start_frame": int(action.start_time * sequence.frame_rate),
                "end_frame": int(action.end_time * sequence.frame_rate),
                "keyframes": action.keyframes,
                "parameters": {
                    "direction": action.direction.value if action.direction else None,
                    "angle": action.angle,
                    "steps": action.steps,
                    "intensity": action.intensity.value,
                    "body_parts": [part.value for part in action.primary_body_parts],
                },
            }
            blender_data["actions"].append(action_data)

        return blender_data

    async def _execute_blender_generation(
        self, blender_data: dict[str, Any], request: AnimationRequest
    ) -> str:
        """
        执行Blender动画生成
        """
        # 保存临时数据文件
        temp_data_file = os.path.join(
            self.temp_dir, f"animation_data_{int(time.time())}.json"
        )
        with open(temp_data_file, "w", encoding="utf-8") as f:
            json.dump(blender_data, f, ensure_ascii=False, indent=2)

        # 生成输出文件路径
        output_filename = f"animation_{request.character_id}_{int(time.time())}.{request.export_format}"
        output_path = os.path.join(self.output_dir, output_filename)

        # 构建Blender命令
        blender_script = "blender_scripts/generate_animation.py"
        cmd = [
            self.blender_path,
            "--background",
            "--python",
            blender_script,
            "--",
            "--input",
            temp_data_file,
            "--output",
            output_path,
            "--format",
            request.export_format,
        ]

        logger.info(f"Executing Blender command: {' '.join(cmd)}")

        # 执行Blender
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
            )

            if result.returncode == 0:
                logger.success(f"Blender animation generated: {output_path}")

                # 清理临时文件
                os.remove(temp_data_file)

                return output_path
            else:
                logger.error(f"Blender execution failed: {result.stderr}")
                raise Exception(f"Blender execution failed: {result.stderr}")

        except subprocess.TimeoutExpired:
            logger.error("Blender execution timed out")
            raise Exception("Blender execution timed out")
        except Exception as e:
            logger.error(f"Error executing Blender: {e}")
            raise

    def _find_blender_executable(self) -> str:
        """
        查找Blender可执行文件
        """
        # 常见的Blender路径
        possible_paths = [
            "/Applications/Blender.app/Contents/MacOS/Blender",  # macOS
            "/usr/bin/blender",  # Linux
            "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe",  # Windows
            "blender",  # 系统PATH中
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 尝试从环境变量获取
        blender_path = os.environ.get("BLENDER_PATH")
        if blender_path and os.path.exists(blender_path):
            return blender_path

        # 默认假设在PATH中
        return "blender"

    def _generate_quality_report(self, sequence: AnimationSequence) -> dict[str, Any]:
        """
        生成质量报告
        """
        return {
            "total_actions": len(sequence.actions),
            "total_duration": sequence.total_duration,
            "frame_rate": sequence.frame_rate,
            "quality_level": sequence.quality_level,
            "action_types": list(set(action.type.value for action in sequence.actions)),
            "complexity_score": self._calculate_complexity_score(sequence),
            "recommendations": self._generate_recommendations(sequence),
        }

    def _calculate_complexity_score(self, sequence: AnimationSequence) -> float:
        """
        计算动画复杂度评分
        """
        base_score = len(sequence.actions) * 10

        # 根据动作类型调整评分
        for action in sequence.actions:
            if action.type.value == "acrobatic":
                base_score += 30
            elif action.type.value == "combat_attack":
                base_score += 20
            elif action.type.value == "locomotion":
                base_score += 10

        return min(100.0, base_score)

    def _generate_recommendations(self, sequence: AnimationSequence) -> list[str]:
        """
        生成优化建议
        """
        recommendations = []

        if sequence.total_duration > 10.0:
            recommendations.append(
                "Consider breaking long sequences into smaller clips"
            )

        if len(sequence.actions) > 10:
            recommendations.append(
                "Complex sequences may benefit from motion capture reference"
            )

        acrobatic_count = sum(
            1 for action in sequence.actions if action.type.value == "acrobatic"
        )
        if acrobatic_count > 3:
            recommendations.append(
                "Multiple acrobatic moves may need additional physics simulation"
            )

        return recommendations
