"""
游戏动画师职能模块
Game Animator Functions - Unified Professional Animator
"""

import math
from typing import Any

import numpy as np
from loguru import logger

from .models import (
    AnimationIntensity,
    AnimationType,
    AnimatorAction,
    BodyPart,
    Direction,
    MotionCaptureData,
)


class UnifiedAnimator:
    """
    统一专业动画师
    - 动捕清理
    - 基础移动动画（走、跑、跳）
    - 循环动画
    - 动作过渡
    - 战斗动画（攻击、防御、受击）
    - 复杂特技动作
    - 动作融合
    - 面部表情动画
    """

    def __init__(self):
        self.frame_rate = 30
        logger.info("Unified Professional Animator initialized")

    def clean_motion_capture(self, mocap_data: MotionCaptureData) -> MotionCaptureData:
        """
        动捕数据清理
        - 去除噪点
        - 平滑曲线
        - 填补缺失帧
        """
        logger.info(f"Cleaning motion capture data: {mocap_data.source_file}")

        # 模拟噪声检测
        noise_level = self._detect_noise_level(mocap_data)

        # 应用滤波器
        cleaned_data = self._apply_noise_filter(mocap_data, noise_level)

        # 填补缺失帧
        if mocap_data.missing_frames:
            cleaned_data = self._interpolate_missing_frames(cleaned_data)

        # 更新状态
        cleaned_data.is_cleaned = True
        cleaned_data.noise_level = noise_level * 0.1  # 大幅降低噪声
        cleaned_data.quality_score = min(0.9, mocap_data.quality_score + 0.3)
        cleaned_data.processing_log.append("Junior animator cleaning applied")

        logger.success("Motion capture data cleaned successfully")
        return cleaned_data

    def create_walk_cycle(self, direction: Direction = Direction.FORWARD,
                         intensity: AnimationIntensity = AnimationIntensity.NORMAL) -> AnimatorAction:
        """创建走路循环动画"""
        logger.info(f"Creating walk cycle: {direction}, {intensity}")

        # 基础走路参数
        base_duration = 1.0
        step_length = 0.8

        # 根据强度调整
        intensity_multipliers = {
            AnimationIntensity.VERY_SLOW: {"duration": 2.0, "step": 0.4},
            AnimationIntensity.SLOW: {"duration": 1.5, "step": 0.6},
            AnimationIntensity.NORMAL: {"duration": 1.0, "step": 0.8},
            AnimationIntensity.FAST: {"duration": 0.7, "step": 1.0},
            AnimationIntensity.VERY_FAST: {"duration": 0.5, "step": 1.2},
        }

        multiplier = intensity_multipliers.get(intensity, intensity_multipliers[AnimationIntensity.NORMAL])

        # 生成关键帧
        keyframes = self._generate_walk_keyframes(
            duration=base_duration * multiplier["duration"],
            step_length=step_length * multiplier["step"],
            direction=direction
        )

        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="walk_cycle",
            direction=direction,
            intensity=intensity,
            duration=base_duration * multiplier["duration"],
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True
        )

        return action

    def create_run_cycle(self, direction: Direction = Direction.FORWARD,
                        intensity: AnimationIntensity = AnimationIntensity.FAST) -> AnimatorAction:
        """创建跑步循环动画"""
        logger.info(f"Creating run cycle: {direction}, {intensity}")

        # 跑步比走路更快，步幅更大
        base_duration = 0.6
        step_length = 1.5

        intensity_multipliers = {
            AnimationIntensity.NORMAL: {"duration": 0.8, "step": 1.2},
            AnimationIntensity.FAST: {"duration": 0.6, "step": 1.5},
            AnimationIntensity.VERY_FAST: {"duration": 0.4, "step": 1.8},
            AnimationIntensity.EXPLOSIVE: {"duration": 0.3, "step": 2.0},
        }

        multiplier = intensity_multipliers.get(intensity, intensity_multipliers[AnimationIntensity.FAST])

        keyframes = self._generate_run_keyframes(
            duration=base_duration * multiplier["duration"],
            step_length=step_length * multiplier["step"],
            direction=direction
        )

        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="run_cycle",
            direction=direction,
            intensity=intensity,
            duration=base_duration * multiplier["duration"],
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True
        )

        return action

    def create_jump_animation(self, direction: Direction = Direction.UP,
                            height: float = 1.0) -> AnimatorAction:
        """创建跳跃动画"""
        logger.info(f"Creating jump animation: {direction}, height={height}")

        # 跳跃分为三个阶段：准备、空中、落地
        total_duration = 1.2

        keyframes = self._generate_jump_keyframes(height, direction, total_duration)

        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name="jump",
            direction=direction,
            intensity=AnimationIntensity.EXPLOSIVE,
            duration=total_duration,
            is_looping=False,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True,
            metadata={"jump_height": height}
        )

        return action

    def create_idle_animation(self, style: str = "breathing") -> AnimatorAction:
        """创建待机动画"""
        logger.info(f"Creating idle animation: {style}")

        if style == "breathing":
            keyframes = self._generate_breathing_keyframes()
            duration = 4.0
        elif style == "looking_around":
            keyframes = self._generate_looking_keyframes()
            duration = 6.0
        else:
            keyframes = self._generate_basic_idle_keyframes()
            duration = 3.0

        action = AnimatorAction(
            type=AnimationType.IDLE,
            name=f"idle_{style}",
            intensity=AnimationIntensity.VERY_SLOW,
            duration=duration,
            is_looping=True,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.UPPER_BODY],
            root_motion=False
        )

        return action

    def create_transition(self, from_action: AnimatorAction,
                         to_action: AnimatorAction) -> AnimatorAction:
        """创建动作过渡"""
        logger.info(f"Creating transition: {from_action.name} -> {to_action.name}")

        transition_duration = 0.3

        # 简单的线性过渡
        keyframes = self._generate_transition_keyframes(
            from_action, to_action, transition_duration
        )

        action = AnimatorAction(
            type=AnimationType.TRANSITION,
            name=f"transition_{from_action.name}_to_{to_action.name}",
            intensity=AnimationIntensity.NORMAL,
            duration=transition_duration,
            is_looping=False,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY]
        )

        return action

    def _detect_noise_level(self, mocap_data: MotionCaptureData) -> float:
        """检测噪声水平"""
        # 模拟噪声检测算法
        return np.random.uniform(0.1, 0.8)

    def _apply_noise_filter(self, mocap_data: MotionCaptureData, noise_level: float) -> MotionCaptureData:
        """应用噪声滤波器"""
        # 模拟滤波处理
        filtered_data = mocap_data.copy()
        filtered_data.processing_log.append(f"Applied noise filter (level: {noise_level:.2f})")
        return filtered_data

    def _interpolate_missing_frames(self, mocap_data: MotionCaptureData) -> MotionCaptureData:
        """插值缺失帧"""
        interpolated_data = mocap_data.copy()
        interpolated_data.missing_frames = []
        interpolated_data.processing_log.append("Interpolated missing frames")
        return interpolated_data

    def _generate_walk_keyframes(self, duration: float, step_length: float,
                               direction: Direction) -> dict[str, Any]:
        """生成走路关键帧"""
        frames = int(duration * self.frame_rate)

        # 简化的走路关键帧数据
        keyframes = {
            "total_frames": frames,
            "root_motion": {
                "translation": self._calculate_walk_translation(step_length, direction, frames),
                "rotation": [0, 0, 0] * frames
            },
            "left_leg": self._generate_leg_cycle(frames, 0),
            "right_leg": self._generate_leg_cycle(frames, frames // 2),
            "left_arm": self._generate_arm_swing(frames, frames // 2),
            "right_arm": self._generate_arm_swing(frames, 0)
        }

        return keyframes

    def _generate_run_keyframes(self, duration: float, step_length: float,
                              direction: Direction) -> dict[str, Any]:
        """生成跑步关键帧"""
        frames = int(duration * self.frame_rate)

        keyframes = {
            "total_frames": frames,
            "root_motion": {
                "translation": self._calculate_run_translation(step_length, direction, frames),
                "rotation": [0, 0, 0] * frames
            },
            "left_leg": self._generate_run_leg_cycle(frames, 0),
            "right_leg": self._generate_run_leg_cycle(frames, frames // 2),
            "left_arm": self._generate_run_arm_swing(frames, frames // 2),
            "right_arm": self._generate_run_arm_swing(frames, 0),
            "spine": self._generate_run_spine_lean(frames)
        }

        return keyframes

    def _generate_jump_keyframes(self, height: float, direction: Direction,
                               duration: float) -> dict[str, Any]:
        """生成跳跃关键帧"""
        frames = int(duration * self.frame_rate)

        # 跳跃分为三个阶段
        prep_frames = frames // 4      # 准备阶段
        air_frames = frames // 2       # 空中阶段
        land_frames = frames - prep_frames - air_frames  # 落地阶段

        keyframes = {
            "total_frames": frames,
            "phases": {
                "preparation": {"start": 0, "end": prep_frames},
                "airborne": {"start": prep_frames, "end": prep_frames + air_frames},
                "landing": {"start": prep_frames + air_frames, "end": frames}
            },
            "root_motion": self._generate_jump_root_motion(height, frames),
            "legs": self._generate_jump_leg_motion(frames, prep_frames, air_frames),
            "arms": self._generate_jump_arm_motion(frames)
        }

        return keyframes

    def _generate_breathing_keyframes(self) -> dict[str, Any]:
        """生成呼吸动画关键帧"""
        frames = int(4.0 * self.frame_rate)  # 4秒循环

        keyframes = {
            "total_frames": frames,
            "chest": self._generate_breathing_chest(frames),
            "spine": self._generate_breathing_spine(frames)
        }

        return keyframes

    def _generate_looking_keyframes(self) -> dict[str, Any]:
        """生成环顾动画关键帧"""
        frames = int(6.0 * self.frame_rate)

        keyframes = {
            "total_frames": frames,
            "head": self._generate_head_looking(frames),
            "eyes": self._generate_eye_movement(frames)
        }

        return keyframes

    def _generate_basic_idle_keyframes(self) -> dict[str, Any]:
        """生成基础待机关键帧"""
        frames = int(3.0 * self.frame_rate)

        keyframes = {
            "total_frames": frames,
            "subtle_sway": self._generate_subtle_sway(frames)
        }

        return keyframes

    def _generate_transition_keyframes(self, from_action: AnimatorAction,
                                     to_action: AnimatorAction,
                                     duration: float) -> dict[str, Any]:
        """生成过渡关键帧"""
        frames = int(duration * self.frame_rate)

        keyframes = {
            "total_frames": frames,
            "blend_curve": "ease_in_out",
            "from_pose": from_action.keyframes.get("end_pose", {}),
            "to_pose": to_action.keyframes.get("start_pose", {})
        }

        return keyframes

    # 辅助方法（简化实现）
    def _calculate_walk_translation(self, step_length: float, direction: Direction, frames: int) -> list[list[float]]:
        """计算走路位移"""
        translation = []
        for i in range(frames):
            progress = i / frames
            if direction == Direction.FORWARD:
                translation.append([0, step_length * progress, 0])
            elif direction == Direction.BACKWARD:
                translation.append([0, -step_length * progress, 0])
            elif direction == Direction.LEFT:
                translation.append([-step_length * progress, 0, 0])
            elif direction == Direction.RIGHT:
                translation.append([step_length * progress, 0, 0])
            else:
                translation.append([0, 0, 0])
        return translation

    def _calculate_run_translation(self, step_length: float, direction: Direction, frames: int) -> list[list[float]]:
        """计算跑步位移"""
        # 跑步位移比走路更大
        return self._calculate_walk_translation(step_length * 1.5, direction, frames)

    def _generate_leg_cycle(self, frames: int, offset: int) -> list[list[float]]:
        """生成腿部循环动画"""
        cycle = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            # 简化的腿部摆动
            rotation = [math.sin(phase) * 30, 0, 0]  # 30度摆动
            cycle.append(rotation)
        return cycle

    def _generate_arm_swing(self, frames: int, offset: int) -> list[list[float]]:
        """生成手臂摆动"""
        swing = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            # 手臂与腿部相反摆动
            rotation = [-math.sin(phase) * 20, 0, 0]  # 20度摆动
            swing.append(rotation)
        return swing

    def _generate_run_leg_cycle(self, frames: int, offset: int) -> list[list[float]]:
        """生成跑步腿部动画"""
        # 跑步时腿部摆动更大
        cycle = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            rotation = [math.sin(phase) * 45, 0, 0]  # 45度摆动
            cycle.append(rotation)
        return cycle

    def _generate_run_arm_swing(self, frames: int, offset: int) -> list[list[float]]:
        """生成跑步手臂摆动"""
        swing = []
        for i in range(frames):
            phase = ((i + offset) % frames) / frames * 2 * math.pi
            rotation = [-math.sin(phase) * 30, 0, 0]  # 30度摆动
            swing.append(rotation)
        return swing

    def _generate_run_spine_lean(self, frames: int) -> list[list[float]]:
        """生成跑步时的脊柱前倾"""
        lean = []
        for i in range(frames):
            # 跑步时轻微前倾
            rotation = [10, 0, 0]  # 10度前倾
            lean.append(rotation)
        return lean

    def _generate_jump_root_motion(self, height: float, frames: int) -> dict[str, Any]:
        """生成跳跃根运动"""
        return {
            "height_curve": [height * math.sin(i / frames * math.pi) for i in range(frames)],
            "translation": [[0, 0, height * math.sin(i / frames * math.pi)] for i in range(frames)]
        }

    def _generate_jump_leg_motion(self, frames: int, prep_frames: int, air_frames: int) -> dict[str, Any]:
        """生成跳跃腿部动作"""
        return {
            "preparation": "crouch_down",
            "takeoff": "extend_legs",
            "airborne": "tuck_legs",
            "landing": "absorb_impact"
        }

    def _generate_jump_arm_motion(self, frames: int) -> dict[str, Any]:
        """生成跳跃手臂动作"""
        return {
            "swing_up": "arms_raise",
            "balance": "arms_spread"
        }

    def _generate_breathing_chest(self, frames: int) -> list[list[float]]:
        """生成胸部呼吸动画"""
        breathing = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 胸部轻微起伏
            scale = [1.0, 1.0 + math.sin(phase) * 0.02, 1.0]
            breathing.append(scale)
        return breathing

    def _generate_breathing_spine(self, frames: int) -> list[list[float]]:
        """生成脊柱呼吸动画"""
        spine = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 脊柱轻微伸展
            rotation = [math.sin(phase) * 2, 0, 0]  # 2度变化
            spine.append(rotation)
        return spine

    def _generate_head_looking(self, frames: int) -> list[list[float]]:
        """生成头部环顾动画"""
        looking = []
        for i in range(frames):
            # 头部左右环顾
            progress = i / frames
            if progress < 0.25:
                rotation = [0, progress * 4 * 30, 0]  # 向右看
            elif progress < 0.5:
                rotation = [0, 30 - (progress - 0.25) * 4 * 60, 0]  # 向左看
            elif progress < 0.75:
                rotation = [0, -30 + (progress - 0.5) * 4 * 60, 0]  # 回到右边
            else:
                rotation = [0, 30 - (progress - 0.75) * 4 * 30, 0]  # 回到中间
            looking.append(rotation)
        return looking

    def _generate_eye_movement(self, frames: int) -> list[list[float]]:
        """生成眼部运动"""
        # 简化的眼部运动
        return [[0, 0, 0] for _ in range(frames)]

    def _generate_subtle_sway(self, frames: int) -> list[list[float]]:
        """生成微妙的摇摆"""
        sway = []
        for i in range(frames):
            phase = i / frames * 2 * math.pi
            # 身体轻微摇摆
            rotation = [0, 0, math.sin(phase) * 1]  # 1度摇摆
            sway.append(rotation)
        return sway

    # Advanced Animation Methods (formerly from IntermediateAnimator)

    def create_combat_attack(self, attack_type: str = "punch",
                           hand: BodyPart = BodyPart.RIGHT_HAND) -> AnimatorAction:
        """创建攻击动画"""
        logger.info(f"Creating combat attack: {attack_type} with {hand}")

        if attack_type == "punch":
            return self._create_punch_animation(hand)
        elif attack_type == "kick":
            return self._create_kick_animation()
        elif attack_type == "slash":
            return self._create_slash_animation(hand)
        else:
            return self._create_generic_attack(attack_type, hand)

    def create_hit_reaction(self, hit_direction: Direction = Direction.FORWARD,
                          intensity: AnimationIntensity = AnimationIntensity.NORMAL) -> AnimatorAction:
        """创建受击反应动画"""
        logger.info(f"Creating hit reaction: {hit_direction}, {intensity}")

        duration = 0.8
        if intensity == AnimationIntensity.EXPLOSIVE:
            duration = 1.2
        elif intensity == AnimationIntensity.FAST:
            duration = 0.6

        keyframes = self._generate_hit_reaction_keyframes(hit_direction, intensity, duration)

        action = AnimatorAction(
            type=AnimationType.COMBAT_HIT,
            name=f"hit_reaction_{hit_direction.value}",
            direction=hit_direction,
            intensity=intensity,
            duration=duration,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            hit_frame=5,  # 受击帧在第5帧
            recovery_frame=int(duration * self.frame_rate * 0.7)
        )

        return action

    def create_backflip_720(self) -> AnimatorAction:
        """创建后空翻720度动画"""
        logger.info("Creating backflip 720 degrees")

        duration = 2.0  # 720度需要更长时间

        keyframes = self._generate_backflip_720_keyframes(duration)

        action = AnimatorAction(
            type=AnimationType.ACROBATIC,
            name="backflip_720",
            direction=Direction.BACKWARD,
            angle=720.0,
            intensity=AnimationIntensity.EXPLOSIVE,
            duration=duration,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True,
            metadata={
                "rotation_axis": "x",
                "total_rotations": 2,
                "difficulty": "advanced"
            }
        )

        return action

    def create_advanced_blend(self, actions: list[AnimatorAction],
                            blend_weights: list[float]) -> AnimatorAction:
        """创建高级动作融合"""
        logger.info(f"Creating advanced blend of {len(actions)} actions")

        if len(actions) != len(blend_weights):
            raise ValueError("Actions and blend weights must have same length")

        # 计算融合后的持续时间
        max_duration = max(action.duration for action in actions)

        # 生成融合关键帧
        keyframes = self._generate_blend_keyframes(actions, blend_weights, max_duration)

        action = AnimatorAction(
            type=AnimationType.BLEND,
            name=f"blend_{len(actions)}_actions",
            duration=max_duration,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            metadata={
                "source_actions": [action.name for action in actions],
                "blend_weights": blend_weights
            }
        )

        return action

    def create_facial_expression(self, expression: str = "smile") -> AnimatorAction:
        """创建面部表情动画"""
        logger.info(f"Creating facial expression: {expression}")

        expression_data = {
            "smile": {"duration": 1.5, "intensity": 0.8},
            "frown": {"duration": 1.0, "intensity": 0.6},
            "surprise": {"duration": 0.8, "intensity": 1.0},
            "anger": {"duration": 1.2, "intensity": 0.9},
            "sad": {"duration": 2.0, "intensity": 0.7}
        }

        data = expression_data.get(expression, expression_data["smile"])

        keyframes = self._generate_facial_keyframes(expression, data["intensity"])

        action = AnimatorAction(
            type=AnimationType.EMOTE,
            name=f"expression_{expression}",
            duration=data["duration"],
            keyframes=keyframes,
            primary_body_parts=[BodyPart.HEAD],
            root_motion=False,
            metadata={"expression_type": expression}
        )

        return action

    def create_complex_turn(self, angle: float, steps: int = 0) -> AnimatorAction:
        """创建复杂转身动画"""
        logger.info(f"Creating complex turn: {angle} degrees, {steps} steps")

        # 根据角度确定转身类型
        if abs(angle) <= 90:
            turn_type = "quarter_turn"
            duration = 0.5
        elif abs(angle) <= 180:
            turn_type = "half_turn"
            duration = 0.8
        elif abs(angle) <= 360:
            turn_type = "full_turn"
            duration = 1.2
        else:
            turn_type = "multi_turn"
            duration = 1.5

        # 如果有步数，增加移动
        if steps > 0:
            duration += steps * 0.3

        keyframes = self._generate_complex_turn_keyframes(angle, steps, duration)

        action = AnimatorAction(
            type=AnimationType.LOCOMOTION,
            name=f"{turn_type}_{int(angle)}deg",
            angle=angle,
            steps=steps,
            duration=duration,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.FULL_BODY],
            root_motion=True
        )

        return action

    def _create_punch_animation(self, hand: BodyPart) -> AnimatorAction:
        """创建拳击动画"""
        duration = 0.6
        keyframes = {
            "total_frames": int(duration * self.frame_rate),
            "phases": {
                "windup": {"start": 0, "end": 8},      # 蓄力
                "strike": {"start": 8, "end": 12},     # 出拳
                "impact": {"start": 12, "end": 14},    # 击中
                "recovery": {"start": 14, "end": 18}   # 收回
            },
            "hand_motion": self._generate_punch_hand_motion(hand, duration),
            "body_rotation": self._generate_punch_body_rotation(duration),
            "weight_shift": self._generate_punch_weight_shift(duration)
        }

        return AnimatorAction(
            type=AnimationType.COMBAT_ATTACK,
            name=f"punch_{hand.value}",
            duration=duration,
            intensity=AnimationIntensity.FAST,
            keyframes=keyframes,
            primary_body_parts=[hand, BodyPart.TORSO],
            hit_frame=12,
            recovery_frame=18
        )

    def _create_kick_animation(self) -> AnimatorAction:
        """创建踢击动画"""
        duration = 0.8
        keyframes = {
            "total_frames": int(duration * self.frame_rate),
            "phases": {
                "lift": {"start": 0, "end": 10},
                "extend": {"start": 10, "end": 15},
                "impact": {"start": 15, "end": 17},
                "retract": {"start": 17, "end": 24}
            },
            "leg_motion": self._generate_kick_leg_motion(duration),
            "balance": self._generate_kick_balance(duration)
        }

        return AnimatorAction(
            type=AnimationType.COMBAT_ATTACK,
            name="kick_front",
            duration=duration,
            intensity=AnimationIntensity.FAST,
            keyframes=keyframes,
            primary_body_parts=[BodyPart.RIGHT_LEG, BodyPart.HIPS],
            hit_frame=15
        )

    def _create_slash_animation(self, hand: BodyPart) -> AnimatorAction:
        """创建斩击动画"""
        duration = 0.9
        keyframes = {
            "total_frames": int(duration * self.frame_rate),
            "arc_motion": self._generate_slash_arc(hand, duration),
            "body_twist": self._generate_slash_body_twist(duration)
        }

        return AnimatorAction(
            type=AnimationType.COMBAT_ATTACK,
            name=f"slash_{hand.value}",
            duration=duration,
            intensity=AnimationIntensity.EXPLOSIVE,
            keyframes=keyframes,
            primary_body_parts=[hand, BodyPart.TORSO, BodyPart.SPINE],
            hit_frame=18
        )

    def _create_generic_attack(self, attack_type: str, hand: BodyPart) -> AnimatorAction:
        """创建通用攻击动画"""
        return AnimatorAction(
            type=AnimationType.COMBAT_ATTACK,
            name=f"{attack_type}_{hand.value}",
            duration=0.7,
            intensity=AnimationIntensity.NORMAL,
            primary_body_parts=[hand]
        )

    def _generate_hit_reaction_keyframes(self, direction: Direction,
                                       intensity: AnimationIntensity,
                                       duration: float) -> dict[str, Any]:
        """生成受击反应关键帧"""
        frames = int(duration * self.frame_rate)

        # 根据受击方向计算反应
        reaction_vector = self._calculate_hit_reaction_vector(direction, intensity)

        return {
            "total_frames": frames,
            "impact_frame": 5,
            "reaction_vector": reaction_vector,
            "body_recoil": self._generate_body_recoil(frames, reaction_vector),
            "head_snap": self._generate_head_snap(frames, direction),
            "recovery": self._generate_hit_recovery(frames)
        }

    def _generate_backflip_720_keyframes(self, duration: float) -> dict[str, Any]:
        """生成720度后空翻关键帧"""
        frames = int(duration * self.frame_rate)

        return {
            "total_frames": frames,
            "rotation_curve": self._generate_720_rotation_curve(frames),
            "height_curve": self._generate_flip_height_curve(frames),
            "body_tuck": self._generate_flip_body_tuck(frames),
            "landing_preparation": self._generate_flip_landing_prep(frames)
        }

    def _generate_blend_keyframes(self, actions: list[AnimatorAction],
                                weights: list[float], duration: float) -> dict[str, Any]:
        """生成融合关键帧"""
        frames = int(duration * self.frame_rate)

        return {
            "total_frames": frames,
            "blend_method": "weighted_average",
            "source_keyframes": [action.keyframes for action in actions],
            "blend_weights": weights,
            "result_curve": self._calculate_blend_curve(actions, weights, frames)
        }

    def _generate_facial_keyframes(self, expression: str, intensity: float) -> dict[str, Any]:
        """生成面部表情关键帧"""
        return {
            "expression_type": expression,
            "intensity": intensity,
            "facial_controls": self._get_facial_controls(expression, intensity),
            "eye_movement": self._generate_expression_eye_movement(expression),
            "mouth_shape": self._generate_mouth_shape(expression, intensity)
        }

    def _generate_complex_turn_keyframes(self, angle: float, steps: int,
                                       duration: float) -> dict[str, Any]:
        """生成复杂转身关键帧"""
        frames = int(duration * self.frame_rate)

        return {
            "total_frames": frames,
            "rotation_angle": angle,
            "step_count": steps,
            "turn_curve": self._generate_turn_curve(angle, frames),
            "step_timing": self._generate_step_timing(steps, frames) if steps > 0 else None,
            "weight_distribution": self._generate_turn_weight_shift(frames)
        }

    # Additional helper methods for advanced animations
    def _generate_punch_hand_motion(self, hand: BodyPart, duration: float) -> dict[str, Any]:
        """生成拳击手部运动"""
        return {"trajectory": "straight_line", "speed_curve": "ease_in_out"}

    def _generate_punch_body_rotation(self, duration: float) -> dict[str, Any]:
        """生成拳击身体旋转"""
        return {"rotation_axis": "y", "max_angle": 15}

    def _generate_punch_weight_shift(self, duration: float) -> dict[str, Any]:
        """生成拳击重心转移"""
        return {"shift_direction": "forward", "amount": 0.3}

    def _generate_kick_leg_motion(self, duration: float) -> dict[str, Any]:
        """生成踢击腿部运动"""
        return {"lift_height": 90, "extension_speed": "fast"}

    def _generate_kick_balance(self, duration: float) -> dict[str, Any]:
        """生成踢击平衡"""
        return {"support_leg": "left", "balance_arms": True}

    def _generate_slash_arc(self, hand: BodyPart, duration: float) -> dict[str, Any]:
        """生成斩击弧线"""
        return {"arc_type": "diagonal", "start_angle": 45, "end_angle": -45}

    def _generate_slash_body_twist(self, duration: float) -> dict[str, Any]:
        """生成斩击身体扭转"""
        return {"twist_axis": "spine", "max_rotation": 30}

    def _calculate_hit_reaction_vector(self, direction: Direction,
                                     intensity: AnimationIntensity) -> list[float]:
        """计算受击反应向量"""
        base_force = {
            AnimationIntensity.NORMAL: 1.0,
            AnimationIntensity.FAST: 1.5,
            AnimationIntensity.EXPLOSIVE: 2.0
        }.get(intensity, 1.0)

        direction_vectors = {
            Direction.FORWARD: [0, -base_force, 0],
            Direction.BACKWARD: [0, base_force, 0],
            Direction.LEFT: [base_force, 0, 0],
            Direction.RIGHT: [-base_force, 0, 0]
        }

        return direction_vectors.get(direction, [0, 0, 0])

    def _generate_body_recoil(self, frames: int, reaction_vector: list[float]) -> list[list[float]]:
        """生成身体后仰"""
        recoil = []
        for i in range(frames):
            progress = i / frames
            # 先后仰，然后恢复
            if progress < 0.3:
                factor = progress / 0.3
            else:
                factor = 1.0 - (progress - 0.3) / 0.7

            recoil.append([v * factor for v in reaction_vector])
        return recoil

    def _generate_head_snap(self, frames: int, direction: Direction) -> list[list[float]]:
        """生成头部急动"""
        return [[0, 0, 0] for _ in range(frames)]  # 简化实现

    def _generate_hit_recovery(self, frames: int) -> dict[str, Any]:
        """生成受击恢复"""
        return {"recovery_curve": "ease_out", "stabilization_frame": int(frames * 0.7)}

    def _generate_720_rotation_curve(self, frames: int) -> list[float]:
        """生成720度旋转曲线"""
        rotation = []
        for i in range(frames):
            progress = i / frames
            # 720度 = 4π弧度
            angle = progress * 4 * math.pi
            rotation.append(math.degrees(angle))
        return rotation

    def _generate_flip_height_curve(self, frames: int) -> list[float]:
        """生成翻转高度曲线"""
        height = []
        for i in range(frames):
            progress = i / frames
            # 抛物线轨迹
            h = 4 * progress * (1 - progress) * 2.0  # 最高2米
            height.append(h)
        return height

    def _generate_flip_body_tuck(self, frames: int) -> dict[str, Any]:
        """生成翻转身体收缩"""
        return {"tuck_phase": "middle_third", "tuck_amount": 0.8}

    def _generate_flip_landing_prep(self, frames: int) -> dict[str, Any]:
        """生成翻转落地准备"""
        return {"prep_start_frame": int(frames * 0.8), "leg_extension": True}

    def _calculate_blend_curve(self, actions: list[AnimatorAction],
                             weights: list[float], frames: int) -> dict[str, Any]:
        """计算融合曲线"""
        return {"method": "weighted_interpolation", "smoothing": True}

    def _get_facial_controls(self, expression: str, intensity: float) -> dict[str, float]:
        """获取面部控制参数"""
        controls = {
            "smile": {"mouth_corner_up": intensity, "cheek_raise": intensity * 0.5},
            "frown": {"mouth_corner_down": intensity, "brow_down": intensity * 0.7},
            "surprise": {"brow_up": intensity, "eye_wide": intensity, "mouth_open": intensity * 0.3},
            "anger": {"brow_down": intensity, "eye_narrow": intensity * 0.8, "mouth_tight": intensity},
            "sad": {"mouth_corner_down": intensity * 0.8, "brow_inner_up": intensity}
        }
        return controls.get(expression, {})

    def _generate_expression_eye_movement(self, expression: str) -> dict[str, Any]:
        """生成表情眼部运动"""
        return {"blink_rate": "normal", "gaze_direction": "forward"}

    def _generate_mouth_shape(self, expression: str, intensity: float) -> dict[str, Any]:
        """生成嘴部形状"""
        return {"shape": expression, "intensity": intensity}

    def _generate_turn_curve(self, angle: float, frames: int) -> list[float]:
        """生成转身曲线"""
        curve = []
        for i in range(frames):
            progress = i / frames
            # 使用ease-in-out曲线
            if progress < 0.5:
                t = 2 * progress * progress
            else:
                t = 1 - 2 * (1 - progress) * (1 - progress)
            curve.append(angle * t)
        return curve

    def _generate_step_timing(self, steps: int, frames: int) -> list[int]:
        """生成步伐时机"""
        if steps == 0:
            return []

        step_frames = []
        step_interval = frames // (steps + 1)
        for i in range(steps):
            step_frames.append((i + 1) * step_interval)
        return step_frames

    def _generate_turn_weight_shift(self, frames: int) -> list[list[float]]:
        """生成转身重心转移"""
        shift = []
        for i in range(frames):
            progress = i / frames
            # 重心在转身过程中的变化
            weight_left = 0.5 + 0.3 * math.sin(progress * math.pi)
            weight_right = 1.0 - weight_left
            shift.append([weight_left, weight_right])
        return shift


# Legacy class aliases for backward compatibility
class JuniorAnimator(UnifiedAnimator):
    """Legacy alias for UnifiedAnimator"""
    def __init__(self):
        super().__init__()
        logger.info("JuniorAnimator (legacy) initialized - using UnifiedAnimator")


class IntermediateAnimator(UnifiedAnimator):
    """Legacy alias for UnifiedAnimator"""
    def __init__(self):
        super().__init__()
        logger.info("IntermediateAnimator (legacy) initialized - using UnifiedAnimator")
