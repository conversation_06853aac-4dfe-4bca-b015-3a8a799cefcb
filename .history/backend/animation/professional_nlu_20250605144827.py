"""
专业游戏动画师NLU管道
Professional Game Animator NLU Pipeline using spaCy + Transformers + Haystack
"""

import re
from typing import Any

import spacy
from loguru import logger
from pydantic import BaseModel

from .models import (
    AnimationIntensity,
    AnimationRequest,
    AnimationResponse,
    AnimationSequence,
    AnimationType,
    AnimatorAction,
    BodyPart,
    Direction,
)


class SceneSegment(BaseModel):
    index: int
    scene: str | None = None
    camera_motion: str | None = None
    environment: list[str] | None = None
    sound: list[str] | None = None
    duration: float | None = None
    actions: list[AnimatorAction] = []


class ProfessionalAnimatorNLU:
    """
    专业动画师自然语言理解系统
    将自然语言转换为动画师专业术语
    """

    def __init__(self):
        self.nlp = self._load_spacy_model()
        self.sentiment_analyzer = self._load_sentiment_model()
        self.action_classifier = self._load_action_classifier()

        # 专业术语映射
        self.action_vocabulary = self._build_action_vocabulary()
        self.direction_patterns = self._build_direction_patterns()
        self.intensity_patterns = self._build_intensity_patterns()
        self.body_part_patterns = self._build_body_part_patterns()
        # 新增：场景、镜头、环境、音效关键词
        self.scene_patterns = self._build_scene_patterns()
        self.camera_patterns = self._build_camera_patterns()
        self.environment_patterns = self._build_environment_patterns()
        self.sound_patterns = self._build_sound_patterns()

        logger.info("Professional Animator NLU initialized")

    def _load_spacy_model(self):
        """加载spaCy模型"""
        try:
            # 尝试加载中文模型
            nlp = spacy.load("zh_core_web_sm")
            logger.info("Loaded Chinese spaCy model")
        except OSError:
            try:
                # 回退到英文模型
                nlp = spacy.load("en_core_web_sm")
                logger.info("Loaded English spaCy model")
            except OSError:
                logger.warning("No spaCy model found, using blank model")
                nlp = spacy.blank("zh")

        return nlp

    def _load_sentiment_model(self):
        """加载情感分析模型"""
        try:
            # 使用简单的规则替代大模型，避免下载延迟
            logger.info("Using rule-based sentiment analysis instead of large model")
            return None  # 将使用规则替代
        except Exception as e:
            logger.warning(f"Failed to load sentiment model: {e}")
            return None

    def _load_action_classifier(self):
        """加载动作分类模型"""
        try:
            # 使用简单的规则替代大模型，避免下载延迟
            logger.info("Using rule-based action classification instead of large model")
            return None  # 将使用规则替代
        except Exception as e:
            logger.warning(f"Failed to load action classifier: {e}")
            return None

    def _build_action_vocabulary(self) -> dict[str, dict[str, Any]]:
        """构建动作词汇表"""
        return {
            # 基础移动
            "走": {
                "type": AnimationType.LOCOMOTION,
                "name": "walk",
                "intensity": AnimationIntensity.NORMAL,
            },
            "跑": {
                "type": AnimationType.LOCOMOTION,
                "name": "run",
                "intensity": AnimationIntensity.FAST,
            },
            "跳": {
                "type": AnimationType.LOCOMOTION,
                "name": "jump",
                "intensity": AnimationIntensity.FAST,
            },
            "蹦": {
                "type": AnimationType.LOCOMOTION,
                "name": "hop",
                "intensity": AnimationIntensity.NORMAL,
            },
            "冲刺": {
                "type": AnimationType.LOCOMOTION,
                "name": "sprint",
                "intensity": AnimationIntensity.VERY_FAST,
            },
            # 特技动作
            "后空翻": {
                "type": AnimationType.ACROBATIC,
                "name": "backflip",
                "angle": 360,
                "intensity": AnimationIntensity.EXPLOSIVE,
            },
            "前空翻": {
                "type": AnimationType.ACROBATIC,
                "name": "frontflip",
                "angle": 360,
                "intensity": AnimationIntensity.EXPLOSIVE,
            },
            "侧翻": {
                "type": AnimationType.ACROBATIC,
                "name": "sideflip",
                "angle": 360,
                "intensity": AnimationIntensity.EXPLOSIVE,
            },
            "翻滚": {
                "type": AnimationType.ACROBATIC,
                "name": "roll",
                "intensity": AnimationIntensity.FAST,
            },
            "旋转": {
                "type": AnimationType.ACROBATIC,
                "name": "spin",
                "intensity": AnimationIntensity.NORMAL,
            },
            # 转身动作
            "转身": {
                "type": AnimationType.LOCOMOTION,
                "name": "turn",
                "intensity": AnimationIntensity.NORMAL,
            },
            "转向": {
                "type": AnimationType.LOCOMOTION,
                "name": "turn_to",
                "intensity": AnimationIntensity.NORMAL,
            },
            "掉头": {
                "type": AnimationType.LOCOMOTION,
                "name": "turn_around",
                "angle": 180,
                "intensity": AnimationIntensity.NORMAL,
            },
            # 步伐动作
            "迈步": {
                "type": AnimationType.LOCOMOTION,
                "name": "step",
                "steps": 1,
                "intensity": AnimationIntensity.NORMAL,
            },
            "踏步": {
                "type": AnimationType.LOCOMOTION,
                "name": "march",
                "intensity": AnimationIntensity.NORMAL,
            },
            "跨步": {
                "type": AnimationType.LOCOMOTION,
                "name": "stride",
                "intensity": AnimationIntensity.NORMAL,
            },
            # 战斗动作
            "攻击": {
                "type": AnimationType.COMBAT_ATTACK,
                "name": "attack",
                "intensity": AnimationIntensity.FAST,
            },
            "防御": {
                "type": AnimationType.COMBAT_DEFEND,
                "name": "defend",
                "intensity": AnimationIntensity.NORMAL,
            },
            "格挡": {
                "type": AnimationType.COMBAT_DEFEND,
                "name": "block",
                "intensity": AnimationIntensity.FAST,
            },
            "闪避": {
                "type": AnimationType.COMBAT_DEFEND,
                "name": "dodge",
                "intensity": AnimationIntensity.VERY_FAST,
            },
            # 手势动作
            "挥手": {
                "type": AnimationType.GESTURE,
                "name": "wave",
                "body_parts": [BodyPart.RIGHT_HAND],
            },
            "指向": {
                "type": AnimationType.GESTURE,
                "name": "point",
                "body_parts": [BodyPart.RIGHT_HAND],
            },
            "鼓掌": {
                "type": AnimationType.GESTURE,
                "name": "clap",
                "body_parts": [BodyPart.LEFT_HAND, BodyPart.RIGHT_HAND],
            },
            # 待机动作
            "站立": {
                "type": AnimationType.IDLE,
                "name": "idle_stand",
                "is_looping": True,
            },
            "等待": {
                "type": AnimationType.IDLE,
                "name": "idle_wait",
                "is_looping": True,
            },
            "呼吸": {
                "type": AnimationType.IDLE,
                "name": "idle_breathe",
                "is_looping": True,
            },
        }

    def _build_direction_patterns(self) -> dict[str, Direction]:
        """构建方向模式"""
        return {
            "向前": Direction.FORWARD,
            "前进": Direction.FORWARD,
            "往前": Direction.FORWARD,
            "向后": Direction.BACKWARD,
            "后退": Direction.BACKWARD,
            "往后": Direction.BACKWARD,
            "向左": Direction.LEFT,
            "左边": Direction.LEFT,
            "往左": Direction.LEFT,
            "向右": Direction.RIGHT,
            "右边": Direction.RIGHT,
            "往右": Direction.RIGHT,
            "向上": Direction.UP,
            "上方": Direction.UP,
            "向下": Direction.DOWN,
            "下方": Direction.DOWN,
        }

    def _build_intensity_patterns(self) -> dict[str, AnimationIntensity]:
        """构建强度模式"""
        return {
            "很慢": AnimationIntensity.VERY_SLOW,
            "慢慢": AnimationIntensity.SLOW,
            "缓慢": AnimationIntensity.SLOW,
            "正常": AnimationIntensity.NORMAL,
            "快速": AnimationIntensity.FAST,
            "迅速": AnimationIntensity.FAST,
            "很快": AnimationIntensity.VERY_FAST,
            "极快": AnimationIntensity.VERY_FAST,
            "爆发": AnimationIntensity.EXPLOSIVE,
            "猛烈": AnimationIntensity.EXPLOSIVE,
        }

    def _build_body_part_patterns(self) -> dict[str, BodyPart]:
        """构建身体部位模式"""
        return {
            "左手": BodyPart.LEFT_HAND,
            "右手": BodyPart.RIGHT_HAND,
            "左臂": BodyPart.LEFT_ARM,
            "右臂": BodyPart.RIGHT_ARM,
            "左腿": BodyPart.LEFT_LEG,
            "右腿": BodyPart.RIGHT_LEG,
            "左脚": BodyPart.LEFT_FOOT,
            "右脚": BodyPart.RIGHT_FOOT,
            "头部": BodyPart.HEAD,
            "身体": BodyPart.TORSO,
            "全身": BodyPart.FULL_BODY,
            "上半身": BodyPart.UPPER_BODY,
            "下半身": BodyPart.LOWER_BODY,
        }

    def _build_scene_patterns(self) -> dict[str, str]:
        return {
            "全景": "panoramic",
            "特写": "closeup",
            "中景": "medium",
            "远景": "longshot",
            "wide shot": "panoramic",
            "close-up": "closeup",
            "medium shot": "medium",
            "long shot": "longshot",
        }

    def _build_camera_patterns(self) -> dict[str, str]:
        return {
            "推镜": "push_in",
            "推进": "push_in",
            "拉镜": "pull_out",
            "上摇": "tilt_up",
            "下摇": "tilt_down",
            "缓推": "slow_push_in",
            "pan": "pan",
            "tilt": "tilt",
            "dolly": "dolly",
        }

    def _build_environment_patterns(self) -> dict[str, str]:
        return {
            "风沙": "sandstorm",
            "黄沙": "sandstorm",
            "雾": "fog",
            "烟雾": "fog",
            "雨": "rain",
            "雪": "snow",
            "沙尘": "sandstorm",
        }

    def _build_sound_patterns(self) -> dict[str, str]:
        return {
            "战鼓": "battle_drum",
            "鼓声": "drum",
            "风声": "wind",
            "呐喊": "shout",
            "士兵呐喊": "soldier_shout",
            "风沙声": "sandstorm_wind",
            "music": "music",
            "sound": "sound",
        }

    async def process_natural_language(
        self, request: AnimationRequest
    ) -> AnimationResponse:
        """
        处理自然语言输入，转换为专业动画术语，支持多场景/多段落
        """
        logger.info(f"Processing animation request: {request.text}")

        try:
            # 1. 文本预处理
            processed_text = self._preprocess_text(request.text)

            # 2. 多段落分割（如1. ... 2. ...）
            segments = self._split_scenes(processed_text)
            scene_segments = []
            for idx, seg in enumerate(segments):
                # 2.1 spaCy分析
                doc = self.nlp(seg)
                # 2.2 提取动作
                actions = self._extract_action_sequence(doc, seg)
                # 2.3 验证和优化
                validated_actions = self._validate_and_optimize(actions)
                # 2.4 场景、镜头、环境、音效、时长
                scene = self._extract_scene(seg)
                camera_motion = self._extract_camera_motion(seg)
                environment = self._extract_environment(seg)
                sound = self._extract_sound(seg)
                duration = self._extract_duration(seg)
                scene_segments.append(
                    SceneSegment(
                        index=idx + 1,
                        scene=scene,
                        camera_motion=camera_motion,
                        environment=environment,
                        sound=sound,
                        duration=duration,
                        actions=validated_actions,
                    )
                )

            # 3. 兼容单段老逻辑
            if len(scene_segments) == 1:
                seg = scene_segments[0]
                animation_sequence = self._create_animation_sequence(
                    seg.actions, request.character_id, request.frame_rate, seg.duration
                )
                response = AnimationResponse(
                    success=True,
                    animation_sequence=animation_sequence,
                    original_text=request.text,
                    processed_actions=[action.name for action in seg.actions],
                    processing_time=0.0,
                    scene=seg.scene,
                    camera_motion=seg.camera_motion,
                    environment=seg.environment,
                    sound=seg.sound,
                    scenes=[seg.model_dump() for seg in scene_segments],
                )
            else:
                # 多段合成，actions拼接，时长累加
                all_actions = []
                total_duration = 0.0
                for seg in scene_segments:
                    for a in seg.actions:
                        if seg.duration:
                            a.duration = (
                                seg.duration / len(seg.actions)
                                if seg.actions
                                else seg.duration
                            )
                        all_actions.append(a)
                        total_duration += a.duration
                animation_sequence = self._create_animation_sequence(
                    all_actions,
                    request.character_id,
                    request.frame_rate,
                    total_duration,
                )
                response = AnimationResponse(
                    success=True,
                    animation_sequence=animation_sequence,
                    original_text=request.text,
                    processed_actions=[action.name for action in all_actions],
                    processing_time=0.0,
                    scenes=[seg.model_dump() for seg in scene_segments],
                )
            logger.success(
                f"Successfully processed {sum(len(seg.actions) for seg in scene_segments)} actions in {len(scene_segments)} scenes"
            )
            return response

        except Exception as e:
            logger.error(f"Error processing animation request: {e}")
            return AnimationResponse(
                success=False, original_text=request.text, error_message=str(e)
            )

    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 移除多余空格
        text = re.sub(r"\s+", " ", text.strip())

        # 标准化数字表达
        text = re.sub(r"(\d+)度", r"\1度", text)
        text = re.sub(r"(\d+)步", r"\1步", text)

        return text

    def _extract_action_sequence(self, doc, text: str) -> list[AnimatorAction]:
        """提取动作序列"""
        actions = []

        # 使用正则表达式分割复合动作
        action_segments = self._split_compound_actions(text)

        for segment in action_segments:
            action = self._parse_single_action(segment)
            if action:
                actions.append(action)

        return actions

    def _split_compound_actions(self, text: str) -> list[str]:
        """分割复合动作"""
        # 分割连接词
        connectors = ["然后", "接着", "之后", "再", "并", "同时"]
        segments = [text]

        for connector in connectors:
            new_segments = []
            for segment in segments:
                new_segments.extend(segment.split(connector))
            segments = [s.strip() for s in new_segments if s.strip()]

        return segments

    def _parse_single_action(self, text: str) -> AnimatorAction | None:
        """解析单个动作"""
        # 查找动作关键词
        action_info = None
        for keyword, info in self.action_vocabulary.items():
            if keyword in text:
                action_info = info.copy()
                break

        if not action_info:
            return None

        # 创建基础动作
        action = AnimatorAction(
            type=action_info["type"],
            name=action_info["name"],
            intensity=action_info.get("intensity", AnimationIntensity.NORMAL),
        )

        # 提取数值参数
        self._extract_numeric_parameters(text, action)

        # 提取方向
        direction = self._extract_direction(text)
        if direction:
            action.direction = direction

        # 提取身体部位
        body_parts = self._extract_body_parts(text)
        if body_parts:
            action.primary_body_parts = body_parts

        # 设置默认值
        if action_info.get("angle"):
            action.angle = action_info["angle"]
        if action_info.get("steps"):
            action.steps = action_info["steps"]
        if action_info.get("body_parts"):
            action.primary_body_parts = action_info["body_parts"]
        if action_info.get("is_looping"):
            action.is_looping = action_info["is_looping"]

        return action

    def _extract_numeric_parameters(self, text: str, action: AnimatorAction):
        """提取数值参数"""
        # 提取角度
        angle_match = re.search(r"(\d+)度", text)
        if angle_match:
            action.angle = float(angle_match.group(1))

        # 提取步数
        steps_match = re.search(r"(\d+)步", text)
        if steps_match:
            action.steps = int(steps_match.group(1))

        # 提取距离
        distance_match = re.search(r"(\d+(?:\.\d+)?)米", text)
        if distance_match:
            action.distance = float(distance_match.group(1))

    def _extract_direction(self, text: str) -> Direction | None:
        """提取方向"""
        for pattern, direction in self.direction_patterns.items():
            if pattern in text:
                return direction
        return None

    def _extract_body_parts(self, text: str) -> list[BodyPart]:
        """提取身体部位"""
        parts = []
        for pattern, part in self.body_part_patterns.items():
            if pattern in text:
                parts.append(part)
        return parts

    def _validate_and_optimize(
        self, actions: list[AnimatorAction]
    ) -> list[AnimatorAction]:
        """验证和优化动作序列"""
        if not actions:
            # 如果没有识别到动作，添加默认待机动作
            return [
                AnimatorAction(
                    type=AnimationType.IDLE,
                    name="idle_stand",
                    duration=2.0,
                    is_looping=True,
                )
            ]

        # 设置时间轴
        current_time = 0.0
        for action in actions:
            action.start_time = current_time

            # 根据动作类型设置默认持续时间
            if action.duration == 1.0:  # 默认值
                action.duration = self._get_default_duration(action)

            action.end_time = action.start_time + action.duration
            current_time = action.end_time

        return actions

    def _get_default_duration(self, action: AnimatorAction) -> float:
        """获取默认持续时间"""
        duration_map = {
            AnimationType.LOCOMOTION: 2.0,
            AnimationType.ACROBATIC: 1.5,
            AnimationType.COMBAT_ATTACK: 0.8,
            AnimationType.COMBAT_DEFEND: 0.6,
            AnimationType.GESTURE: 1.2,
            AnimationType.IDLE: 3.0,
            AnimationType.TRANSITION: 0.5,
        }

        base_duration = duration_map.get(action.type, 1.0)

        # 根据强度调整
        intensity_multiplier = {
            AnimationIntensity.VERY_SLOW: 2.0,
            AnimationIntensity.SLOW: 1.5,
            AnimationIntensity.NORMAL: 1.0,
            AnimationIntensity.FAST: 0.7,
            AnimationIntensity.VERY_FAST: 0.5,
            AnimationIntensity.EXPLOSIVE: 0.3,
        }

        return base_duration * intensity_multiplier.get(action.intensity, 1.0)

    def _create_animation_sequence(
        self,
        actions: list[AnimatorAction],
        character_id: str,
        frame_rate: int | None,
        total_duration: float | None,
    ) -> AnimationSequence:
        """创建动画序列"""
        # Ensure frame_rate is not None, use default value if needed
        if frame_rate is None:
            frame_rate = 30
            logger.warning("frame_rate was None, using default value of 30")

        # Calculate total duration, handling None end_time values
        if actions:
            valid_end_times = [
                action.end_time for action in actions if action.end_time is not None
            ]
            total_duration = max(valid_end_times) if valid_end_times else 0.0
        else:
            total_duration = 0.0
        total_frames = int(total_duration * frame_rate)

        return AnimationSequence(
            name=f"generated_sequence_{character_id}",
            actions=actions,
            total_duration=total_duration,
            frame_rate=frame_rate,
            total_frames=total_frames,
            character_id=character_id,
        )

    def _extract_scene(self, text: str) -> str | None:
        for pattern, scene in self.scene_patterns.items():
            if pattern in text:
                return scene
        return None

    def _extract_camera_motion(self, text: str) -> str | None:
        for pattern, camera in self.camera_patterns.items():
            if pattern in text:
                return camera
        return None

    def _extract_environment(self, text: str) -> list[str]:
        envs = []
        for pattern, env in self.environment_patterns.items():
            if pattern in text:
                envs.append(env)
        return list(set(envs))

    def _extract_sound(self, text: str) -> list[str]:
        sounds = []
        for pattern, sound in self.sound_patterns.items():
            if pattern in text:
                sounds.append(sound)
        return list(set(sounds))

    def _split_scenes(self, text: str) -> list[str]:
        """按1. ... 2. ...等分割多场景段落"""
        import re

        # 支持"1."、"2."、"一、二、"等
        pattern = r"(?:\d+\.|[一二三四五六七八九十]+、)"
        splits = [m.start() for m in re.finditer(pattern, text)]
        if not splits or splits[0] != 0:
            splits = [0] + splits
        segments = []
        for i in range(len(splits)):
            start = splits[i]
            end = splits[i + 1] if i + 1 < len(splits) else len(text)
            seg = text[start:end].strip()
            if seg:
                segments.append(seg)
        return segments

    def _extract_duration(self, text: str) -> float | None:
        import re

        match = re.search(r"(\d+(?:\.\d+)?)\s*[秒sS]", text)
        if match:
            return float(match.group(1))
        return None
