"""
LangGraph-based NLU Pipeline for Motion Generation
Advanced natural language understanding using LangChain and LangGraph
"""

from typing import Any, TypedDict

from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import END, StateGraph
from loguru import logger

from .models import Action, ActionSequence, ActionType, MotionRequest, MotionResponse
from .utils import validate_action_sequence


class MotionState(TypedDict):
    """State for the motion generation graph"""

    original_text: str
    character_id: str
    processed_text: str
    extracted_actions: list[dict[str, Any]]
    action_sequence: ActionSequence | None
    validation_errors: list[str]
    success: bool
    error_message: str | None


class LangGraphNLUPipeline:
    """
    Advanced NLU pipeline using LangGraph for structured motion generation
    """

    def __init__(self):
        self.graph = self._build_graph()
        self.action_extraction_prompt = self._create_action_extraction_prompt()
        self.validation_prompt = self._create_validation_prompt()

    def _create_action_extraction_prompt(self) -> ChatPromptTemplate:
        """Create prompt for extracting actions from natural language"""
        system_message = """You are an expert motion analysis AI that extracts structured action data from natural language descriptions.

Your task is to analyze the input text and extract individual actions with their properties.

For each action, identify:
1. Action type: locomotion, gesture, pose, interaction, expression
2. Action name: specific action (walk, run, wave, sit, etc.)
3. Duration: estimated time in seconds
4. Parameters: additional details like intensity, direction, target body parts

Output format should be a JSON list of actions:
[
  {
    "type": "locomotion",
    "name": "walk",
    "duration": 3.0,
    "parameters": {
      "direction": "forward",
      "intensity": "slow",
      "target_parts": []
    }
  }
]

Supported action types and examples:
- locomotion: walk, run, jump, hop, skip, march
- gesture: wave, point, clap, nod, shake, bow
- pose: sit, stand, crouch, kneel, lie, lean
- interaction: pick, grab, push, pull, throw, catch
- expression: smile, frown, blink

If no clear actions are found, return an idle pose action."""

        return ChatPromptTemplate.from_messages(
            [
                ("system", system_message),
                ("human", "Extract actions from this text: {text}"),
            ]
        )

    def _create_validation_prompt(self) -> ChatPromptTemplate:
        """Create prompt for validating action sequences"""
        system_message = """You are a motion validation expert. Your task is to check if a sequence of actions is physically possible and realistic.

Check for:
1. Impossible transitions (e.g., jumping directly from sitting)
2. Conflicting simultaneous actions
3. Unrealistic durations
4. Missing intermediate actions

If issues are found, suggest corrections. Output should be JSON:
{
  "is_valid": true/false,
  "issues": ["list of issues"],
  "suggestions": ["list of suggestions"]
}"""

        return ChatPromptTemplate.from_messages(
            [
                ("system", system_message),
                ("human", "Validate this action sequence: {actions}"),
            ]
        )

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        workflow = StateGraph(MotionState)

        # Add nodes
        workflow.add_node("preprocess", self._preprocess_text)
        workflow.add_node("extract_actions", self._extract_actions)
        workflow.add_node("validate_sequence", self._validate_sequence)
        workflow.add_node("create_sequence", self._create_action_sequence)
        workflow.add_node("handle_error", self._handle_error)

        # Add edges
        workflow.set_entry_point("preprocess")
        workflow.add_edge("preprocess", "extract_actions")
        workflow.add_edge("extract_actions", "validate_sequence")
        workflow.add_conditional_edges(
            "validate_sequence",
            self._should_retry,
            {"create_sequence": "create_sequence", "handle_error": "handle_error"},
        )
        workflow.add_edge("create_sequence", END)
        workflow.add_edge("handle_error", END)

        return workflow.compile()

    async def _preprocess_text(self, state: MotionState) -> MotionState:
        """Preprocess the input text"""
        logger.debug(f"Preprocessing text: {state['original_text']}")

        # Basic text cleaning and normalization
        processed_text = state["original_text"].lower().strip()
        processed_text = " ".join(processed_text.split())  # Normalize whitespace

        state["processed_text"] = processed_text
        logger.debug(f"Processed text: {processed_text}")

        return state

    async def _extract_actions(self, state: MotionState) -> MotionState:
        """Extract actions using LangChain prompt"""
        logger.debug("Extracting actions from processed text")

        try:
            # For now, we'll use a simplified extraction without LLM
            # In production, you would use an actual LLM here
            extracted_actions = self._simple_action_extraction(state["processed_text"])

            state["extracted_actions"] = extracted_actions
            logger.info(f"Extracted {len(extracted_actions)} actions")

        except Exception as e:
            logger.error(f"Error extracting actions: {e}")
            state["success"] = False
            state["error_message"] = f"Action extraction failed: {str(e)}"

        return state

    def _simple_action_extraction(self, text: str) -> list[dict[str, Any]]:
        """Simplified action extraction for demonstration"""
        actions = []

        # Basic keyword matching
        action_keywords = {
            "walk": {"type": "locomotion", "duration": 3.0},
            "run": {"type": "locomotion", "duration": 2.0},
            "jump": {"type": "locomotion", "duration": 1.0},
            "wave": {"type": "gesture", "duration": 2.0},
            "sit": {"type": "pose", "duration": 2.0},
            "stand": {"type": "pose", "duration": 1.0},
            "clap": {"type": "gesture", "duration": 1.5},
            "nod": {"type": "gesture", "duration": 1.0},
        }

        words = text.split()
        for word in words:
            if word in action_keywords:
                action_data = action_keywords[word]
                action = {
                    "type": action_data["type"],
                    "name": word,
                    "duration": action_data["duration"],
                    "parameters": {
                        "intensity": self._extract_intensity(text),
                        "direction": self._extract_direction(text),
                        "target_parts": [],
                    },
                }
                actions.append(action)

        # Default idle action if nothing found
        if not actions:
            actions.append(
                {"type": "pose", "name": "idle", "duration": 2.0, "parameters": {}}
            )

        return actions

    def _extract_intensity(self, text: str) -> str:
        """Extract intensity from text"""
        if any(word in text for word in ["slow", "slowly", "gentle"]):
            return "slow"
        elif any(word in text for word in ["fast", "quick", "rapidly"]):
            return "fast"
        return "normal"

    def _extract_direction(self, text: str) -> str | None:
        """Extract direction from text"""
        directions = ["forward", "backward", "left", "right", "up", "down"]
        for direction in directions:
            if direction in text:
                return direction
        return None

    async def _validate_sequence(self, state: MotionState) -> MotionState:
        """Validate the extracted action sequence"""
        logger.debug("Validating action sequence")

        try:
            actions = state["extracted_actions"]
            is_valid, issues = validate_action_sequence(actions)

            state["validation_errors"] = issues

            if is_valid:
                logger.info("Action sequence validation passed")
            else:
                logger.warning(f"Validation issues found: {issues}")

        except Exception as e:
            logger.error(f"Error during validation: {e}")
            state["validation_errors"] = [f"Validation error: {str(e)}"]

        return state

    def _should_retry(self, state: MotionState) -> str:
        """Decide whether to proceed or handle errors"""
        if state.get("validation_errors") and len(state["validation_errors"]) > 3:
            return "handle_error"
        return "create_sequence"

    async def _create_action_sequence(self, state: MotionState) -> MotionState:
        """Create the final action sequence"""
        logger.debug("Creating action sequence")

        try:
            actions = []
            current_time = 0.0

            for action_data in state["extracted_actions"]:
                action = Action(
                    type=ActionType(action_data["type"]),
                    name=action_data["name"],
                    duration=action_data["duration"],
                    start_time=current_time,
                    parameters=action_data.get("parameters", {}),
                )
                actions.append(action)
                current_time += action.duration

            action_sequence = ActionSequence(
                actions=actions,
                total_duration=current_time,
                character_id=state["character_id"],
            )

            state["action_sequence"] = action_sequence
            state["success"] = True
            logger.success(f"Created action sequence with {len(actions)} actions")

        except Exception as e:
            logger.error(f"Error creating action sequence: {e}")
            state["success"] = False
            state["error_message"] = f"Failed to create action sequence: {str(e)}"

        return state

    async def _handle_error(self, state: MotionState) -> MotionState:
        """Handle errors in the pipeline"""
        logger.error("Handling pipeline error")

        state["success"] = False
        if not state.get("error_message"):
            state["error_message"] = "Pipeline validation failed"

        return state

    async def process(self, request: MotionRequest) -> MotionResponse:
        """
        Process motion request through the LangGraph pipeline
        """
        logger.info(f"Processing motion request with LangGraph: {request.text}")

        # Initialize state
        initial_state = MotionState(
            original_text=request.text,
            character_id=request.character_id,
            processed_text="",
            extracted_actions=[],
            action_sequence=None,
            validation_errors=[],
            success=False,
            error_message=None,
        )

        try:
            # Run the graph
            final_state = await self.graph.ainvoke(initial_state)

            # Create response
            if final_state["success"]:
                response = MotionResponse(
                    success=True,
                    action_sequence=final_state["action_sequence"],
                    original_text=request.text,
                    processed_text=final_state["processed_text"],
                    blender_script_path="blender_scripts/generate_animation.py",
                )
                logger.success("LangGraph pipeline completed successfully")
            else:
                response = MotionResponse(
                    success=False,
                    error_message=final_state["error_message"],
                    original_text=request.text,
                )
                logger.error(
                    f"LangGraph pipeline failed: {final_state['error_message']}"
                )

            return response

        except Exception as e:
            logger.exception(f"LangGraph pipeline error: {str(e)}")
            return MotionResponse(
                success=False, error_message=str(e), original_text=request.text
            )

    def to_animatediff_format(self, action_sequence: ActionSequence) -> dict:
        """
        将标准动作序列转换为 AnimateDiff 所需格式
        """
        # 假设 AnimateDiff 需要 {"actions": [...], "total_frames": int, ...}
        actions = []
        total_frames = 0
        for action in action_sequence.actions:
            frames = int(action.duration * 30)  # 假设30fps
            actions.append(
                {
                    "name": action.name,
                    "type": action.type,
                    "start_frame": int(action.start_time * 30),
                    "end_frame": int((action.start_time + action.duration) * 30),
                    "keyframes": action.parameters.get("keyframes", {}),
                }
            )
            total_frames = max(
                total_frames, int((action.start_time + action.duration) * 30)
            )
        return {
            "actions": actions,
            "total_frames": total_frames,
            "character_id": action_sequence.character_id,
        }

    def to_text2motion_format(self, action_sequence: ActionSequence) -> dict:
        """
        将标准动作序列转换为 Text2Motion 所需格式
        """
        # 假设 Text2Motion 需要 {"motion": [...], "length": int, ...}
        motion = []
        length = 0
        for action in action_sequence.actions:
            frames = int(action.duration * 30)
            motion.append(
                {
                    "action": action.name,
                    "type": action.type,
                    "start": int(action.start_time * 30),
                    "duration": frames,
                    "params": action.parameters,
                }
            )
            length = max(length, int((action.start_time + action.duration) * 30))
        return {
            "motion": motion,
            "length": length,
            "character_id": action_sequence.character_id,
        }
