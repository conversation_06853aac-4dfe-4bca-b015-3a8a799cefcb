"""
任务模型
Task Models
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel, Field
from sqlalchemy import Column, DateTime, String, Text, JSON, Boolean, Foreign<PERSON>ey, Integer, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class TaskType(str, Enum):
    """任务类型枚举"""
    ANIMATION_GENERATION = "animation_generation"
    NLU_PROCESSING = "nlu_processing"
    MOTION_CAPTURE = "motion_capture"
    BLENDER_EXPORT = "blender_export"
    CONVERSATION_PROCESSING = "conversation_processing"
    SYSTEM_MAINTENANCE = "system_maintenance"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Task(Base):
    """任务数据库模型"""
    __tablename__ = "tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(String(255), unique=True, nullable=False)  # taskiq 任务ID
    
    # 任务基本信息
    task_type = Column(String(50), nullable=False)
    task_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    priority = Column(String(20), default=TaskPriority.NORMAL.value)
    
    # 状态信息
    status = Column(String(20), default=TaskStatus.PENDING.value)
    progress = Column(Float, default=0.0)  # 进度百分比 0-100
    
    # 关联信息
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversation_threads.id"), nullable=True)
    message_id = Column(UUID(as_uuid=True), ForeignKey("messages.id"), nullable=True)
    user_id = Column(String(255), nullable=True)
    
    # 任务参数和结果
    input_data = Column(JSON, default=dict)
    output_data = Column(JSON, default=dict)
    metadata = Column(JSON, default=dict)
    
    # 执行信息
    worker_id = Column(String(255), nullable=True)
    queue_name = Column(String(255), nullable=True)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    estimated_duration = Column(Integer, nullable=True)  # 预估时长（秒）
    actual_duration = Column(Integer, nullable=True)  # 实际时长（秒）
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # 标记
    is_cancelled = Column(Boolean, default=False)
    is_system_task = Column(Boolean, default=False)


class TaskCreate(BaseModel):
    """创建任务请求模型"""
    task_type: TaskType = Field(..., description="任务类型")
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="任务优先级")
    
    # 关联信息
    conversation_id: Optional[str] = Field(None, description="对话ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    
    # 任务参数
    input_data: dict[str, Any] = Field(default_factory=dict, description="输入数据")
    metadata: dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    # 执行配置
    queue_name: Optional[str] = Field(None, description="队列名称")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")
    estimated_duration: Optional[int] = Field(None, ge=1, description="预估时长（秒）")


class TaskUpdate(BaseModel):
    """更新任务请求模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    progress: Optional[float] = Field(None, ge=0, le=100, description="进度百分比")
    output_data: Optional[dict[str, Any]] = Field(None, description="输出数据")
    metadata: Optional[dict[str, Any]] = Field(None, description="元数据")
    worker_id: Optional[str] = Field(None, description="工作节点ID")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    actual_duration: Optional[int] = Field(None, description="实际时长（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[dict[str, Any]] = Field(None, description="错误详情")


class TaskResponse(BaseModel):
    """任务响应模型"""
    id: str = Field(..., description="任务数据库ID")
    task_id: str = Field(..., description="任务ID")
    
    # 任务基本信息
    task_type: TaskType = Field(..., description="任务类型")
    task_name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: TaskPriority = Field(..., description="任务优先级")
    
    # 状态信息
    status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(..., description="进度百分比")
    
    # 关联信息
    conversation_id: Optional[str] = Field(None, description="对话ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    
    # 任务数据
    input_data: dict[str, Any] = Field(..., description="输入数据")
    output_data: dict[str, Any] = Field(..., description="输出数据")
    metadata: dict[str, Any] = Field(..., description="元数据")
    
    # 执行信息
    worker_id: Optional[str] = Field(None, description="工作节点ID")
    queue_name: Optional[str] = Field(None, description="队列名称")
    retry_count: int = Field(..., description="重试次数")
    max_retries: int = Field(..., description="最大重试次数")
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    estimated_duration: Optional[int] = Field(None, description="预估时长（秒）")
    actual_duration: Optional[int] = Field(None, description="实际时长（秒）")
    
    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[dict[str, Any]] = Field(None, description="错误详情")
    
    # 标记
    is_cancelled: bool = Field(..., description="是否已取消")
    is_system_task: bool = Field(..., description="是否系统任务")

    class Config:
        from_attributes = True


class TaskList(BaseModel):
    """任务列表响应模型"""
    tasks: list[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class TaskFilter(BaseModel):
    """任务过滤条件"""
    task_type: Optional[TaskType] = Field(None, description="任务类型过滤")
    status: Optional[TaskStatus] = Field(None, description="状态过滤")
    priority: Optional[TaskPriority] = Field(None, description="优先级过滤")
    conversation_id: Optional[str] = Field(None, description="对话ID过滤")
    user_id: Optional[str] = Field(None, description="用户ID过滤")
    is_system_task: Optional[bool] = Field(None, description="是否系统任务过滤")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    # 排序参数
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="排序方向")


class TaskStats(BaseModel):
    """任务统计模型"""
    total_tasks: int = Field(..., description="总任务数")
    pending_tasks: int = Field(..., description="待处理任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    cancelled_tasks: int = Field(..., description="已取消任务数")
    average_duration: Optional[float] = Field(None, description="平均执行时长（秒）")
    success_rate: float = Field(..., description="成功率（百分比）")
