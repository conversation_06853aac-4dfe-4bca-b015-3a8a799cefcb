"""
对话线程模型
Conversation Thread Models
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel, Field
from sqlalchemy import Column, DateTime, String, Text, JSON, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class ConversationStatus(str, Enum):
    """对话状态枚举"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class ConversationThread(Base):
    """对话线程数据库模型"""
    __tablename__ = "conversation_threads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(20), default=ConversationStatus.ACTIVE.value)
    
    # 用户信息
    user_id = Column(String(255), nullable=True)
    session_id = Column(String(255), nullable=True)
    
    # 配置信息
    character_id = Column(String(255), default="default")
    context = Column(JSON, default=dict)
    settings = Column(JSON, default=dict)
    
    # 统计信息
    message_count = Column(String(20), default="0")
    total_tokens = Column(String(20), default="0")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity_at = Column(DateTime, default=datetime.utcnow)
    
    # 标记
    is_deleted = Column(Boolean, default=False)
    is_pinned = Column(Boolean, default=False)
    
    # 关联关系
    # messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")


class ConversationThreadCreate(BaseModel):
    """创建对话线程请求模型"""
    title: str = Field(..., min_length=1, max_length=255, description="对话标题")
    description: Optional[str] = Field(None, max_length=1000, description="对话描述")
    character_id: str = Field(default="default", description="角色ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    settings: dict[str, Any] = Field(default_factory=dict, description="设置信息")


class ConversationThreadUpdate(BaseModel):
    """更新对话线程请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="对话标题")
    description: Optional[str] = Field(None, max_length=1000, description="对话描述")
    status: Optional[ConversationStatus] = Field(None, description="对话状态")
    character_id: Optional[str] = Field(None, description="角色ID")
    context: Optional[dict[str, Any]] = Field(None, description="上下文信息")
    settings: Optional[dict[str, Any]] = Field(None, description="设置信息")
    is_pinned: Optional[bool] = Field(None, description="是否置顶")


class ConversationThreadResponse(BaseModel):
    """对话线程响应模型"""
    id: str = Field(..., description="对话ID")
    title: str = Field(..., description="对话标题")
    description: Optional[str] = Field(None, description="对话描述")
    status: ConversationStatus = Field(..., description="对话状态")
    
    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    
    # 配置信息
    character_id: str = Field(..., description="角色ID")
    context: dict[str, Any] = Field(..., description="上下文信息")
    settings: dict[str, Any] = Field(..., description="设置信息")
    
    # 统计信息
    message_count: int = Field(..., description="消息数量")
    total_tokens: int = Field(..., description="总token数")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_activity_at: datetime = Field(..., description="最后活动时间")
    
    # 标记
    is_pinned: bool = Field(..., description="是否置顶")

    class Config:
        from_attributes = True


class ConversationThreadList(BaseModel):
    """对话线程列表响应模型"""
    threads: list[ConversationThreadResponse] = Field(..., description="对话线程列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class ConversationThreadFilter(BaseModel):
    """对话线程过滤条件"""
    status: Optional[ConversationStatus] = Field(None, description="状态过滤")
    user_id: Optional[str] = Field(None, description="用户ID过滤")
    character_id: Optional[str] = Field(None, description="角色ID过滤")
    is_pinned: Optional[bool] = Field(None, description="是否置顶过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    # 排序参数
    sort_by: str = Field(default="last_activity_at", description="排序字段")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="排序方向")
