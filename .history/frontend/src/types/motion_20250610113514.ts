// Motion Agent TypeScript Types
export interface TextInput {
  text: string;
  character_id: string;
}

export interface AdvancedTextInput extends TextInput {
  use_langgraph: boolean;
  context?: Record<string, unknown>;
}

export interface AnimationRequest {
  text: string;
  character_id: string;
  quality_target: string;
  frame_rate: number;
  export_format: string;
  context?: Record<string, unknown>;
  reference_animations: string[];
}

export interface MotionResponse {
  success: boolean;
  message: string;
  action_sequence?: {
    actions: string[];
    duration: number;
    complexity?: string;
  };
  error_message?: string;
}

export interface AnimationResponse {
  success: boolean;
  animation_sequence?: {
    id: string;
    name: string;
    actions: unknown[];
    total_duration: number;
    frame_rate: number;
    total_frames: number;
    character_id: string;
  };
  fbx_file_path?: string;
  original_text: string;
  processed_actions: string[];
  quality_report: Record<string, unknown>;
  error_message?: string;
  warnings: string[];
  processing_time?: number;
}

export interface AnimationPresets {
  locomotion: string[];
  acrobatic: string[];
  combat: string[];
  gestures: string[];
  expressions: string[];
  idle: string[];
}

export interface ExampleRequest {
  text: string;
  description: string;
  animator_level: string;
}

export interface HealthStatus {
  status: string;
  nlu_pipeline?: string;
  langgraph_pipeline?: string;
  professional_animator?: string;
  version: string;
  features: Record<string, boolean>;
}
