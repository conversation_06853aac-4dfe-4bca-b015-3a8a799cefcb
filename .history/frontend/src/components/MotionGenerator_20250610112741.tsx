'use client';

import { useState, useEffect } from 'react';
import {
  generateProfessionalAnimation,
  getAnimationPresets,
  handleApiError
} from '@/lib/api';
import {
  AnimationRequest,
  AnimationResponse,
  AnimationPresets
} from '@/types/motion';
import { Setting<PERSON>, Sparkles } from 'lucide-react';
import toast from 'react-hot-toast';

interface MotionGeneratorProps {
  onResponse: (response: AnimationResponse) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
  selectedExample?: string;
}

export default function MotionGenerator({ 
  onResponse, 
  onLoading, 
  onError, 
  selectedExample 
}: MotionGeneratorProps) {
  const [text, setText] = useState('');
  const [characterId, setCharacterId] = useState('default');
  const [useLanggraph, setUseLanggraph] = useState(true);
  const [presets, setPresets] = useState<AnimationPresets | null>(null);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Update text when example is selected
  useEffect(() => {
    if (selectedExample) {
      setText(selectedExample);
    }
  }, [selectedExample]);

  // Load animation presets
  useEffect(() => {
    const loadPresets = async () => {
      try {
        const presetsData = await getAnimationPresets();
        setPresets(presetsData);
      } catch (error) {
        console.error('Failed to load presets:', error);
      }
    };
    loadPresets();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!text.trim()) {
      toast.error('Please enter an animation description');
      return;
    }

    onLoading(true);
    onError(null);

    try {
      const animationRequest: AnimationRequest = {
        text: text.trim(),
        character_id: characterId,
        quality_target: 'game_ready',
        frame_rate: 30,
        export_format: 'fbx',
        reference_animations: []
      };

      const response = await generateProfessionalAnimation(animationRequest);

      if (response.success) {
        onResponse(response);
        toast.success('Animation generated successfully!');
      } else {
        const errorMsg = response.error_message || 'Animation generation failed';
        onError(errorMsg);
        toast.error(errorMsg);
      }
    } catch (error) {
      const errorMsg = handleApiError(error);
      onError(errorMsg);
      toast.error(errorMsg);
    } finally {
      onLoading(false);
    }
  };

  const insertPreset = (preset: string) => {
    const currentText = text.trim();
    const newText = currentText ? `${currentText}, ${preset}` : preset;
    setText(newText);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-3 mb-6">
        <Sparkles className="w-6 h-6 text-blue-600" />
        <h2 className="text-xl font-bold">Motion Generator</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Text Input */}
        <div>
          <label htmlFor="text" className="block text-sm font-medium text-gray-700 mb-2">
            Animation Description
          </label>
          <textarea
            id="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Describe the animation you want to generate... (e.g., 'walk forward three steps, then jump and land')"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={4}
            required
          />
        </div>

        {/* Quick Presets */}
        {presets && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Actions (click to add)
            </label>
            <div className="space-y-2">
              {Object.entries(presets).map(([category, actions]) => (
                <div key={category}>
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                    {category}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {actions.slice(0, 6).map((action) => (
                      <button
                        key={action}
                        type="button"
                        onClick={() => insertPreset(action)}
                        className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                      >
                        {action}
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Advanced Options Toggle */}
        <button
          type="button"
          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
        >
          <Settings className="w-4 h-4" />
          {showAdvancedOptions ? 'Hide' : 'Show'} Advanced Options
        </button>

        {/* Advanced Options */}
        {showAdvancedOptions && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="characterId" className="block text-sm font-medium text-gray-700 mb-2">
                  Character ID
                </label>
                <input
                  id="characterId"
                  type="text"
                  value={characterId}
                  onChange={(e) => setCharacterId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>


            </div>

            {generationType === 'advanced' && (
              <div className="flex items-center">
                <input
                  id="useLanggraph"
                  type="checkbox"
                  checked={useLanggraph}
                  onChange={(e) => setUseLanggraph(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="useLanggraph" className="ml-2 block text-sm text-gray-700">
                  Use LangGraph Pipeline
                </label>
              </div>
            )}
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
        >
          Generate Animation
        </button>
      </form>
    </div>
  );
}
