# 专业游戏动画师系统配置
# Professional Game Animator System Configuration with Taskiq and MongoDB

# 应用配置
APP_NAME=Motion Agent
APP_VERSION=3.0.0
APP_DESCRIPTION=Professional Game Animator with Taskiq and MongoDB
ENVIRONMENT=development

# API配置
API_HOST=0.0.0.0
API_PORT=9000
API_RELOAD=true
API_WORKERS=1
DEBUG=true

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*

# MongoDB 数据库配置
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=motion_agent
MONGODB_USERNAME=
MONGODB_PASSWORD=
MONGODB_AUTH_SOURCE=admin
MONGODB_MAX_CONNECTIONS=100
MONGODB_MIN_CONNECTIONS=10
MONGODB_MAX_IDLE_TIME=30000
MONGODB_CONNECT_TIMEOUT=10000
MONGODB_SERVER_SELECTION_TIMEOUT=5000

# Redis 配置 (for Taskiq)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_RESULT_HOST=localhost
REDIS_RESULT_PORT=6379
REDIS_RESULT_PASSWORD=
REDIS_RESULT_DB=1
REDIS_MAX_CONNECTIONS=50
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# Blender配置
BLENDER_PATH=/Applications/Blender.app/Contents/MacOS/Blender
# BLENDER_PATH=/usr/bin/blender  # Linux
# BLENDER_PATH=C:\Program Files\Blender Foundation\Blender\blender.exe  # Windows

# 角色模型配置
DEFAULT_CHARACTER_MODEL=models/default_character.blend
CHARACTER_MODELS_DIR=models/characters/

# 动画设置
DEFAULT_FRAME_RATE=30
DEFAULT_ANIMATION_QUALITY=game_ready
DEFAULT_EXPORT_FORMAT=fbx

# 输出配置
OUTPUT_DIR=output/animations
TEMP_DIR=temp/animation_data
LOG_DIR=logs

# 日志配置
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
LOG_FORMAT="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"

# NLU配置
SPACY_MODEL=zh_core_web_sm
# SPACY_MODEL=en_core_web_sm  # 英文模型
TRANSFORMERS_MODEL=microsoft/DialoGPT-medium
HAYSTACK_ENABLED=true

# 动画师级别配置
DEFAULT_ANIMATOR_LEVEL=intermediate
ENABLE_JUNIOR_FUNCTIONS=true
ENABLE_INTERMEDIATE_FUNCTIONS=true
ENABLE_SENIOR_FUNCTIONS=false

# 性能配置
MAX_ANIMATION_DURATION=30.0
MAX_ACTIONS_PER_SEQUENCE=15
PROCESSING_TIMEOUT=300
BLENDER_TIMEOUT=300

# 质量配置
ENABLE_QUALITY_ASSESSMENT=true
ENABLE_OPTIMIZATION_SUGGESTIONS=true
COMPLEXITY_THRESHOLD=80

# 缓存配置
ENABLE_CACHING=true
CACHE_DIR=cache/
CACHE_EXPIRY=3600

# Taskiq 任务队列配置
TASKIQ_WORKER_CONCURRENCY=4
TASKIQ_MAX_RETRIES=3
TASKIQ_RETRY_DELAY=60
TASKIQ_DEFAULT_QUEUE=default
TASKIQ_ANIMATION_QUEUE=animation
TASKIQ_CONVERSATION_QUEUE=conversation
TASKIQ_NLU_QUEUE=nlu
TASKIQ_SYSTEM_QUEUE=system
TASKIQ_DEFAULT_TIMEOUT=300
TASKIQ_ANIMATION_TIMEOUT=600
TASKIQ_CONVERSATION_TIMEOUT=180
TASKIQ_NLU_TIMEOUT=60

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_FILE=motion_agent.log
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
LOG_COMPRESSION=gz
ENABLE_ACCESS_LOG=true
ENABLE_ERROR_LOG=true
ENABLE_TASK_LOG=true

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1
ENABLE_API_KEY_AUTH=false
API_KEY_HEADER=X-API-Key
VALID_API_KEYS=
SESSION_TIMEOUT=3600
MAX_SESSIONS_PER_USER=5

# 外部服务配置
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# 开发配置
DEVELOPMENT_MODE=true
ENABLE_PROFILING=false
ENABLE_METRICS=true
ENABLE_SWAGGER_UI=true
ENABLE_REDOC=true
ENABLE_TEST_ENDPOINTS=true
TEST_DATA_CLEANUP=true
