-- Motion Agent 数据库初始化脚本
-- Motion Agent Database Initialization Script

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建索引函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 设置时区
SET timezone = 'UTC';

-- 创建用户和权限（如果需要）
-- CREATE USER motion_agent_user WITH PASSWORD 'motion_agent_password';
-- GRANT ALL PRIVILEGES ON DATABASE motion_agent TO motion_agent_user;

-- 输出初始化完成信息
SELECT 'Motion Agent database initialized successfully' AS status;
