#!/usr/bin/env python3
"""
配置化的后端启动脚本
Configurable Backend Startup Script
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn
from loguru import logger

# 导入配置
from backend.config import get_config, validate_config


def setup_logging():
    """设置日志"""
    config = get_config()
    
    logger.remove()  # 移除默认处理器
    
    # 控制台日志
    logger.add(
        sys.stdout,
        format=config.logging.log_format_console,
        level=config.logging.log_level,
    )
    
    # 文件日志
    os.makedirs(config.logging.log_dir, exist_ok=True)
    log_file_path = f"{config.logging.log_dir}/{config.logging.log_file}"
    logger.add(
        log_file_path,
        rotation=config.logging.log_rotation,
        retention=config.logging.log_retention,
        compression=config.logging.log_compression,
        format=config.logging.log_format_file,
        level=config.logging.log_level,
    )


def main():
    """主函数"""
    setup_logging()
    
    logger.info("🚀 Starting Motion Agent Backend Server...")
    
    # 获取配置
    config = get_config()
    
    # 验证配置
    if not validate_config():
        logger.error("Configuration validation failed, exiting...")
        sys.exit(1)
    
    # 显示配置信息
    logger.info(f"App Name: {config.app_name}")
    logger.info(f"Version: {config.app_version}")
    logger.info(f"Environment: {config.environment}")
    logger.info(f"Debug Mode: {config.development.debug}")
    logger.info(f"API Host: {config.api.api_host}")
    logger.info(f"API Port: {config.api.api_port}")
    logger.info(f"MongoDB: {config.database.mongodb_url}/{config.database.mongodb_database}")
    logger.info(f"Redis: {config.redis.redis_host}:{config.redis.redis_port}")
    
    # 显示服务地址
    logger.info("🌐 Service URLs:")
    logger.info(f"  - API Root: http://{config.api.api_host}:{config.api.api_port}/")
    logger.info(f"  - API Docs: http://{config.api.api_host}:{config.api.api_port}/docs")
    logger.info(f"  - Health Check: http://{config.api.api_host}:{config.api.api_port}/health")
    logger.info(f"  - Conversations: http://{config.api.api_host}:{config.api.api_port}/conversations/")
    logger.info(f"  - Tasks: http://{config.api.api_host}:{config.api.api_port}/tasks/")
    
    # 启动服务器
    try:
        uvicorn.run(
            "backend.app:app",
            host=config.api.api_host,
            port=config.api.api_port,
            reload=config.api.api_reload,
            workers=config.api.api_workers if not config.api.api_reload else 1,
            log_level=config.logging.log_level.lower(),
            access_log=config.logging.enable_access_log,
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
