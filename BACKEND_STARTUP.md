# Motion Agent Backend Startup Guide

## 快速启动 (Quick Start)

### 方法1: 使用简单启动脚本 (推荐)
```bash
# 在项目根目录运行
python start_backend.py
```

### 方法2: 使用uv命令
```bash
# 安装依赖
uv sync

# 启动后端
uv run python start_backend.py
```

### 方法3: 使用项目脚本
```bash
# 安装依赖
pip install -e .

# 启动后端
start-backend
```

### 方法4: 直接使用uvicorn
```bash
# 确保在项目根目录
python -m uvicorn backend.app:app --host 0.0.0.0 --port 9000 --reload
```

## 依赖管理统一说明

### 新的依赖管理结构
- ✅ **统一管理**: 所有依赖现在在根目录的 `pyproject.toml` 中统一管理
- ✅ **简化安装**: 只需要在根目录运行 `uv sync` 或 `pip install -e .`
- ✅ **版本一致**: 避免了backend和根目录依赖版本不一致的问题

### 已整合的依赖
```toml
# 核心框架
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# 日志和工具
loguru>=0.7.2
python-multipart>=0.0.6
python-dotenv>=1.0.0
aiofiles>=23.2.1

# AI/ML相关
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.10
langgraph>=0.0.20
transformers>=4.30
torch>=2.0
spacy>=3.5
nltk>=3.8.1

# 数据处理
numpy>=1.24.3
scipy>=1.11.0
matplotlib>=3.7.0
opencv-python>=4.8.0

# 其他
requests>=2.31.0
jinja2>=3.1.2
haystack>=1.20
```

## 服务器信息

启动成功后，服务器将在以下地址可用:

- **API根路径**: http://localhost:9000/
- **API文档**: http://localhost:9000/docs
- **ReDoc文档**: http://localhost:9000/redoc  
- **健康检查**: http://localhost:9000/health
- **专业动画师**: http://localhost:9000/animation/

## API使用示例

### 基础动作生成
```bash
curl -X POST 'http://localhost:9000/generate-motion' \
     -H 'Content-Type: application/json' \
     -d '{
       "text": "慢慢走向前方，然后挥手打招呼",
       "character_id": "test_character"
     }'
```

### 高级动作生成 (LangGraph)
```bash
curl -X POST 'http://localhost:9000/generate-motion-advanced' \
     -H 'Content-Type: application/json' \
     -d '{
       "text": "生成一个后空翻720度之后，向前五步走，然后转身",
       "character_id": "test_character",
       "use_langgraph": true
     }'
```

### 专业动画师API
```bash
curl -X POST 'http://localhost:9000/animation/generate' \
     -H 'Content-Type: application/json' \
     -d '{
       "text": "快速冲刺攻击，然后防御姿态",
       "character_id": "test_character",
       "animator_level": "intermediate"
     }'
```

## 故障排除

### 常见问题

1. **依赖缺失错误**
   ```bash
   # 重新安装依赖
   uv sync
   # 或者
   pip install -e .
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :9000
   # 杀死占用进程
   kill -9 <PID>
   ```

3. **导入错误**
   ```bash
   # 确保在项目根目录
   pwd
   # 检查PYTHONPATH
   export PYTHONPATH=$PWD:$PYTHONPATH
   ```

4. **权限问题**
   ```bash
   # 给启动脚本执行权限
   chmod +x start_backend.py
   ```

### 开发模式

启动脚本默认使用 `--reload` 模式，代码修改后会自动重启服务器。

### 生产模式

生产环境建议使用:
```bash
python -m uvicorn backend.app:app --host 0.0.0.0 --port 9000 --workers 4
```

## 项目结构

```
motion-agent/
├── pyproject.toml          # 统一依赖管理
├── start_backend.py        # 简单启动脚本
├── start_professional_animator.py  # 完整启动脚本
├── backend/
│   ├── app.py             # FastAPI应用入口
│   ├── nlu/               # NLU管道
│   ├── animation/         # 动画模块
│   └── models.py          # 数据模型
├── blender_scripts/       # Blender脚本
└── logs/                  # 日志目录
```

## 下一步

1. 启动后端服务器
2. 访问 http://localhost:9000/docs 查看API文档
3. 测试API端点
4. 集成Blender脚本进行动画生成
