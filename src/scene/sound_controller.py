from typing import Any

import bpy


class SoundController:
    def __init__(self):
        self.sounds = {}

    def add_sound(self, name: str, filepath: str, settings: dict[str, Any]):
        # 创建声音
        sound = bpy.data.sounds.new(name=name, filepath=filepath)

        # 设置声音参数
        sound.volume = settings.get("volume", 1.0)
        sound.pitch = settings.get("pitch", 1.0)

        self.sounds[name] = sound

    def create_sound_animation(self, name: str, keyframes: list[dict[str, Any]]):
        if name not in self.sounds:
            return

        sound = self.sounds[name]

        for frame, data in enumerate(keyframes):
            sound.volume = data.get("volume", 1.0)
            sound.pitch = data.get("pitch", 1.0)
            sound.keyframe_insert(data_path="volume", frame=frame)
            sound.keyframe_insert(data_path="pitch", frame=frame)
