from typing import Any

import spacy
from haystack import Pipeline
from transformers import pipeline


class NLUPipeline:
    def __init__(self):
        # 加载 spaCy 模型
        self.nlp = spacy.load("zh_core_web_sm")

        # 初始化 Transformers pipeline
        self.classifier = pipeline(
            "text-classification",
            model="bert-base-chinese",
            tokenizer="bert-base-chinese",
        )

        # 初始化 Haystack pipeline
        self.haystack_pipeline = Pipeline()

        # 自定义动作类型映射
        self.action_types = {
            "走": "walk",
            "跑": "run",
            "跳": "jump",
            "攻击": "attack",
            "防御": "defend",
            "技能": "skill",
        }

    def process_text(self, text: str) -> dict[str, Any]:
        # 使用 spaCy 进行基础 NLP 处理
        doc = self.nlp(text)

        # 提取动作类型
        action_type = self._extract_action_type(doc)

        # 提取动作参数
        params = self._extract_action_params(doc)

        # 使用 Transformers 进行意图分类
        intent = self.classifier(text)[0]

        return {"action_type": action_type, "params": params, "intent": intent}

    def _extract_action_type(self, doc) -> str:
        for token in doc:
            if token.text in self.action_types:
                return self.action_types[token.text]
        return "unknown"

    def _extract_action_params(self, doc) -> dict[str, Any]:
        params = {}
        for token in doc:
            if token.like_num:
                params["steps"] = int(token.text)
            elif token.dep_ == "nmod":
                params["direction"] = token.text
        return params
