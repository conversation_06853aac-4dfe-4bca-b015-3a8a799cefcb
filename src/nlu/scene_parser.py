from typing import Any


class SceneParser:
    def __init__(self):
        self.scene_elements = {
            "camera": ["全景", "特写", "推镜", "拉镜", "摇镜"],
            "environment": ["黄沙", "风沙", "雨", "雪"],
            "sound": ["战鼓", "呐喊", "风沙声"],
            "character": ["主角", "关羽", "士兵"],
        }

    def parse_scene(self, scene_text: str) -> dict[str, Any]:
        scene_data = {
            "camera": self._extract_camera(scene_text),
            "environment": self._extract_environment(scene_text),
            "sound": self._extract_sound(scene_text),
            "character": self._extract_character(scene_text),
            "duration": self._extract_duration(scene_text),
        }
        return scene_data

    def _extract_camera(self, text: str) -> list[str]:
        return [elem for elem in self.scene_elements["camera"] if elem in text]

    def _extract_environment(self, text: str) -> list[str]:
        return [elem for elem in self.scene_elements["environment"] if elem in text]

    def _extract_sound(self, text: str) -> list[str]:
        return [elem for elem in self.scene_elements["sound"] if elem in text]

    def _extract_character(self, text: str) -> list[str]:
        return [elem for elem in self.scene_elements["character"] if elem in text]

    def _extract_duration(self, text: str) -> int:
        import re

        match = re.search(r"(\d+)秒", text)
        return int(match.group(1)) if match else 0
