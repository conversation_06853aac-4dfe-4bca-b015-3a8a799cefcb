
import numpy as np


class MotionCleaner:
    def __init__(self):
        self.smoothing_window = 5
        self.threshold = 0.1

    def clean_motion_data(self, motion_data: np.ndarray) -> np.ndarray:
        # 应用平滑滤波
        smoothed_data = self._apply_smoothing(motion_data)

        # 移除异常值
        cleaned_data = self._remove_outliers(smoothed_data)

        # 调整动作节奏
        rhythm_adjusted = self._adjust_rhythm(cleaned_data)

        return rhythm_adjusted

    def _apply_smoothing(self, data: np.ndarray) -> np.ndarray:
        window = np.ones(self.smoothing_window) / self.smoothing_window
        return np.convolve(data, window, mode="same")

    def _remove_outliers(self, data: np.ndarray) -> np.ndarray:
        mean = np.mean(data)
        std = np.std(data)
        return np.where(np.abs(data - mean) > std * self.threshold, mean, data)

    def _adjust_rhythm(self, data: np.ndarray) -> np.ndarray:
        # 实现动作节奏调整逻辑
        return data
