2025-06-05 10:55:03 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 10:55:03 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 10:55:03 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 10:55:06 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:56:24 | INFO     | backend.app:generate_motion:128 - Received motion generation request: 慢慢走向前方，然后挥手打招呼
2025-06-05 10:56:24 | DEBUG    | backend.app:generate_motion:136 - Processing motion request for character: test_character
2025-06-05 10:56:24 | SUCCESS  | backend.app:generate_motion:145 - Successfully generated motion simulation
2025-06-05 10:56:54 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:56:59 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:57:05 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 10:58:04 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 10:58:04 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 10:58:04 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 10:58:14 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 11:07:37 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 11:07:37 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 11:07:37 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 11:22:51 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:22:51 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:23:22 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:25:56 | INFO     | app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 11:25:56 | SUCCESS  | app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 11:25:56 | INFO     | app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 11:26:01 | DEBUG    | app:health_check:180 - Health check endpoint accessed
2025-06-05 11:27:43 | INFO     | app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 11:27:43 | SUCCESS  | app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 11:27:43 | INFO     | app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 11:27:47 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:27:47 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:28:01 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:02 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:23 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:28:23 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:28:31 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:32 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:32 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:42 | INFO     | app:generate_motion:120 - Received motion generation request: walk forward and wave
2025-06-05 11:28:42 | DEBUG    | app:generate_motion:128 - Processing motion request for character: test
2025-06-05 11:28:42 | SUCCESS  | app:generate_motion:137 - Successfully generated motion simulation
2025-06-05 11:28:49 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 后空翻然后向前走三步
2025-06-05 11:28:49 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:29:01 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:29:02 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:29:33 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:03 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:11 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:33 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:31:03 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:31:06 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:31:06 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:31:11 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:28 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:45:28 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:45:28 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:45:30 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:34 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:45:34 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:46:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:46:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:46:43 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:46:43 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:47:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:47:34 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:47:49 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:47:49 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:47:49 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:48:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:48:04 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:48:04 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:48:04 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:48:35 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:48:35 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:50:33 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步
2025-06-05 12:50:33 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:51:16 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:51:16 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:51:16 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:51:17 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:51:19 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:51:19 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:51:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:54:07 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:54:07 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:54:07 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:54:38 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:54:38 | ERROR    | backend.app:generate_professional_animation:290 - Error in professional animation generation: No module named 'animation'
Traceback (most recent call last):

  File "/Users/<USER>/project/llm/motion-agent/.venv/bin/uvicorn", line 10, in <module>
    sys.exit(main())
    │   │    └ <Command main>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x102d91620>
           └ <Command main>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x1026d28d0>
         │    └ <function Command.invoke at 0x102d91300>
         └ <Command main>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001, 'app': 'backend.app:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'rel...
           │   │      │    │           └ <click.core.Context object at 0x1026d28d0>
           │   │      │    └ <function main at 0x102fdf4c0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x102d90540>
           └ <click.core.Context object at 0x1026d28d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 9001, 'app': 'backend.app:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'rel...
           │         └ ()
           └ <function main at 0x102fdf4c0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/main.py", line 413, in main
    run(
    └ <function run at 0x102dd45e0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x102e1e3e0>
    └ <uvicorn.server.Server object at 0x102dc8050>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x102e1e480>
           │       │   └ <uvicorn.server.Server object at 0x102dc8050>
           │       └ <function run at 0x102b476a0>
           └ <module 'asyncio' from '/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/__init__....
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x102f87100>
           │      └ <function Runner.run at 0x102b47a60>
           └ <asyncio.runners.Runner object at 0x102feeb90>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-pac...
           │    │     └ <cyfunction Loop.run_until_complete at 0x102fcf370>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x102feeb90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x103031e90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x1053b1f50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x103031e90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x103257450>
          └ <fastapi.applications.FastAPI object at 0x1053b1f50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x103394310>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x103257450>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x103394390>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x103394310>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x1053ec6d0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x103394390>
          └ <function wrap_app_handling_exceptions at 0x104d83f60>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x102fe7cd0>>
          └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Route.handle at 0x104db1620>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <function request_response.<locals>.app at 0x1053ad760>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x1053ec690>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x1053af6a0>
          └ <function wrap_app_handling_exceptions at 0x104d83f60>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af7e0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x1053af6a0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x1053ec690>
                     └ <function get_request_handler.<locals>.app at 0x1053ad620>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x104db1120>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': AnimationRequest(text='向前走三步然后挥手', character_id='test_api_fix', animator_level='intermediate', quality_target='ga...
                 │         └ <function generate_professional_animation at 0x1053ad580>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

> File "/Users/<USER>/project/llm/motion-agent/backend/app.py", line 267, in generate_professional_animation
    from animation.professional_pipeline import ProfessionalAnimationPipeline

ModuleNotFoundError: No module named 'animation'
2025-06-05 12:55:46 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:55:46 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:55:46 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:57:07 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:57:11 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:57:11 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099431.json --output output/animations/animation_default_1749099431.fbx --format fbx
2025-06-05 12:57:12 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099431.fbx
2025-06-05 12:57:12 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 1.08s
2025-06-05 12:57:12 | SUCCESS  | backend.app:generate_professional_animation:289 - Professional animation generation completed
2025-06-05 12:57:21 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 2 actions
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:57:21 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099441.json --output output/animations/animation_test_api_fix_1749099441.fbx --format fbx
2025-06-05 12:57:22 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_test_api_fix_1749099441.fbx
2025-06-05 12:57:22 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.46s
2025-06-05 12:57:22 | SUCCESS  | backend.app:generate_professional_animation:289 - Professional animation generation completed
2025-06-05 12:58:34 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:58:34 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:58:34 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:58:39 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:58:42 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:58:42 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:58:42 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099522.json --output output/animations/animation_default_1749099522.fbx --format fbx
2025-06-05 12:58:43 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099522.fbx
2025-06-05 12:58:43 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.96s
2025-06-05 12:58:43 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:03:38 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 13:03:38 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 13:03:38 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 13:03:57 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:04:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:04:00 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:04:00 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099840.json --output output/animations/animation_default_1749099840.fbx --format fbx
2025-06-05 13:04:01 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099840.fbx
2025-06-05 13:04:01 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.97s
2025-06-05 13:04:01 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:04:03 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:04:33 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:05:03 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:05:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:05:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:05:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:05:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:05:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:05:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:05:34 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:01 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:06:01 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:06:01 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:06:03 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:08 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:06:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:06:11 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:06:11 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099971.json --output output/animations/animation_default_1749099971.fbx --format fbx
2025-06-05 13:06:12 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099971.fbx
2025-06-05 13:06:12 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.94s
2025-06-05 13:06:12 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:06:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:06:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:06:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:06:33 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:45 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:54 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:57 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:07:00 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:07:00 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100020.json --output output/animations/animation_default_1749100020.fbx --format fbx
2025-06-05 13:07:01 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100020.fbx
2025-06-05 13:07:01 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.93s
2025-06-05 13:07:01 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:07:23 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:07:27 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:07:27 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:07:27 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100047.json --output output/animations/animation_default_1749100047.fbx --format fbx
2025-06-05 13:07:28 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100047.fbx
2025-06-05 13:07:28 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 1.01s
2025-06-05 13:07:28 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:07:53 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:08:24 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:08:54 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:09:23 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:09:53 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:10:11 | INFO     | backend.app:root:103 - Root endpoint accessed
2025-06-05 13:10:24 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:10:36 | INFO     | backend.app:root:103 - Root endpoint accessed
2025-06-05 13:10:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:19 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:11:19 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:11:19 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:11:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:51 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:51 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100020.fbx
2025-06-05 13:11:51 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100020.fbx (25852 bytes)
2025-06-05 13:12:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:12:24 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100020.fbx
2025-06-05 13:12:24 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100020.fbx (25852 bytes)
2025-06-05 13:12:34 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:12:37 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:12:37 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:12:37 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100357.json --output output/animations/animation_default_1749100357.fbx --format fbx
2025-06-05 13:12:38 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100357.fbx
2025-06-05 13:12:38 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.88s
2025-06-05 13:12:38 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:12:40 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100357.fbx
2025-06-05 13:12:40 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100357.fbx (25852 bytes)
2025-06-05 13:12:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:13:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:13:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:14:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:14:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:55 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:15:55 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:15:55 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:08 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:08 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:08 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:16:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:02 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:02 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:02 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:17:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:16 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:16 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:16 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:18:46 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:46 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:46 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:06 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:06 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:06 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:23 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:23 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:23 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:19:44 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:44 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:44 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:06 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:20:06 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:20:06 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:12 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:20:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:20:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:20:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:20:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:45 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:21:45 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:21:45 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:21:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:56 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:21:56 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:21:56 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:07 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:07 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:07 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:22:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:32 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:32 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:32 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:22:50 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:50 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:50 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:05 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:05 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:05 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:23:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:45 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:45 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:45 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:24:09 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:24:09 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:24:09 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:24:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:25:01 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:01 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:01 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:21 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:21 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:21 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:25:42 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:42 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:42 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:26:10 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:26:10 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:26:10 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:26:25 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:26:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:26:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:27:10 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:10 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:10 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:22 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:22 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:22 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:27:43 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:43 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:43 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:28:04 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:28:04 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:28:04 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:28:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:28:48 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:28:48 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:28:48 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:28:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:29:18 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:29:18 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:29:18 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:29:21 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:29:51 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:30:18 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:30:21 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:30:21 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 13:30:21 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:30:21 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 13:30:21 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:30:21 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 4 actions
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:process_animation_request:61 - Step 2: Animator Function Processing
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:_apply_animator_functions:103 - Applying unified animator functions
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_walk_cycle:65 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_complex_turn:624 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_run_cycle:106 - Creating run cycle: Direction.FORWARD, AnimationIntensity.FAST
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: backflip -> walk_cycle
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: walk_cycle -> half_turn_180deg
2025-06-05 13:30:21 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: half_turn_180deg -> run_cycle
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:process_animation_request:67 - Step 3: Generate Blender Animation Data
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 4: Execute Blender Animation Generation
2025-06-05 13:30:21 | INFO     | animation.professional_pipeline:_execute_blender_generation:271 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749101421.json --output output/animations/animation_default_1749101421.fbx --format fbx
2025-06-05 13:30:22 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:283 - Blender animation generated: output/animations/animation_default_1749101421.fbx
2025-06-05 13:30:22 | SUCCESS  | animation.professional_pipeline:process_animation_request:87 - Animation generated successfully in 0.91s
2025-06-05 13:30:22 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:30:22 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:30:25 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749101421.fbx
2025-06-05 13:30:25 | INFO     | backend.app:download_animation_file:339 - Serving file: animation_default_1749101421.fbx (25852 bytes)
2025-06-05 13:30:51 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:31:21 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:31:51 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:32:21 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:33:39 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:33:39 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:33:39 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:33:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:34:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:35:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:36:37 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:36:37 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:36:37 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:36:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:37:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:38:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:39:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:39:53 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:39:53 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:39:53 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:40:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:40:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:40:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:40:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:41:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:42:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:42:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:42:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:42:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:43:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:44:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:44:48 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:44:48 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:44:48 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:45:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:46:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:47:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:48:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:49:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:49:50 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:49:50 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:49:50 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:50:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:51:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:52:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:53:41 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:53:41 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:53:41 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:53:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:53:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:53:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:53:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:54:07 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:54:07 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:54:07 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:54:15 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:54:15 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:54:15 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:54:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:54:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:54:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:54:39 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:54:39 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:54:39 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:54:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:54:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:54:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:54:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:55:02 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:55:02 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:55:02 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:55:19 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:55:19 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:55:19 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:55:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:55:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:55:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:55:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:55:42 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:55:42 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:55:42 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:55:53 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:55:53 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:55:53 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:56:04 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:56:04 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:56:04 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:56:17 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:56:17 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:56:17 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:56:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:56:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:56:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:56:40 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:56:40 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:56:40 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:56:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:56:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:56:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:56:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:02 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:02 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:02 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:10 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:10 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:10 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:19 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:19 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:19 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:35 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:35 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:35 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:57:44 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:44 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:44 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:57:53 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:57:53 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:57:53 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:58:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:59:37 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:59:41 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:59:45 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:59:47 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 13:59:47 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 13:59:47 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 13:59:47 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 13:59:47 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步，然后原地向前跑12步，再向右边横跨三步，最后立定。
2025-06-05 13:59:47 | SUCCESS  | animation.professional_nlu:process_natural_language:205 - Successfully processed 4 actions
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:process_animation_request:61 - Step 2: Animator Function Processing
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:_apply_animator_functions:103 - Applying unified animator functions
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_walk_cycle:65 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_complex_turn:624 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_run_cycle:106 - Creating run cycle: Direction.FORWARD, AnimationIntensity.FAST
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: backflip -> walk_cycle
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: walk_cycle -> half_turn_180deg
2025-06-05 13:59:47 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: half_turn_180deg -> run_cycle
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:process_animation_request:67 - Step 3: Generate Blender Animation Data
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 4: Execute Blender Animation Generation
2025-06-05 13:59:47 | INFO     | animation.professional_pipeline:_execute_blender_generation:271 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749103187.json --output output/animations/animation_default_1749103187.fbx --format fbx
2025-06-05 13:59:48 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:283 - Blender animation generated: output/animations/animation_default_1749103187.fbx
2025-06-05 13:59:48 | SUCCESS  | animation.professional_pipeline:process_animation_request:87 - Animation generated successfully in 1.05s
2025-06-05 13:59:48 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:59:55 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749103187.fbx
2025-06-05 13:59:55 | INFO     | backend.app:download_animation_file:339 - Serving file: animation_default_1749103187.fbx (25852 bytes)
2025-06-05 14:00:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:00:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:01:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:01:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:02:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:02:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:03:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:04:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:05:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:06:16 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:06:21 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:06:21 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:06:21 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:06:21 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:06:21 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:06:21 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:06:21 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:06:21 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:06:21 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 14:06:21 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:06:21 | ERROR    | animation.professional_nlu:process_natural_language:209 - Error processing animation request: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-05 14:06:21 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:06:41 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:07:11 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:07:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:08:02 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:08:02 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:08:02 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:08:07 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:08:08 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:08:08 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:08:08 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:08:08 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:08:08 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:08:08 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:08:08 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:08:08 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 14:08:08 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:08:08 | ERROR    | animation.professional_nlu:process_natural_language:209 - Error processing animation request: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-05 14:08:08 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:08:11 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:08:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:08:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:08:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:08:41 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:09:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:09:25 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:09:25 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:09:25 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:09:39 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:09:40 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:09:40 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:09:40 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:09:40 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:09:40 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:09:40 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:09:40 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:09:40 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 14:09:40 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:09:40 | ERROR    | animation.professional_nlu:process_natural_language:209 - Error processing animation request: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-05 14:09:40 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:09:41 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:10:03 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8秒,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:10:03 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:10:03 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:10:03 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:10:03 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:10:03 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:10:03 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:10:03 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8秒,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:10:03 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 14:10:03 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8秒,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:10:03 | ERROR    | animation.professional_nlu:process_natural_language:209 - Error processing animation request: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-05 14:10:03 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:10:11 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:10:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:11:11 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:11:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:11:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:11:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:11:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:12:08 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:12:08 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:12:08 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:12:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:12:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:12:42 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:12:42 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:12:42 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:13:14 | INFO     | app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:13:14 | SUCCESS  | app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:13:14 | INFO     | app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:13:18 | INFO     | app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:13:19 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:13:19 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:13:19 | WARNING  | animation.professional_nlu:_load_spacy_model:49 - No spaCy model found, using blank model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:_load_sentiment_model:58 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:_load_action_classifier:68 - Using rule-based action classification instead of large model
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:__init__:35 - Professional Animator NLU initialized
2025-06-05 14:13:19 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:__init__:43 - Professional Animation Pipeline initialized
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:process_animation_request:50 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:process_animation_request:54 - Step 1: Natural Language Understanding
2025-06-05 14:13:19 | INFO     | animation.professional_nlu:process_natural_language:176 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成：全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声； 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2秒。声音战鼓声，呐喊声
2025-06-05 14:13:19 | SUCCESS  | animation.professional_nlu:process_natural_language:205 - Successfully processed 1 actions
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:process_animation_request:61 - Step 2: Animator Function Processing
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:_apply_animator_functions:103 - Applying unified animator functions
2025-06-05 14:13:19 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:process_animation_request:67 - Step 3: Generate Blender Animation Data
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 4: Execute Blender Animation Generation
2025-06-05 14:13:19 | INFO     | animation.professional_pipeline:_execute_blender_generation:271 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749103999.json --output output/animations/animation_default_1749103999.fbx --format fbx
2025-06-05 14:13:20 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:283 - Blender animation generated: output/animations/animation_default_1749103999.fbx
2025-06-05 14:13:20 | SUCCESS  | animation.professional_pipeline:process_animation_request:87 - Animation generated successfully in 0.91s
2025-06-05 14:13:20 | SUCCESS  | app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:13:35 | INFO     | app:download_animation_file:324 - Download request for file: animation_default_1749103999.fbx
2025-06-05 14:13:35 | INFO     | app:download_animation_file:339 - Serving file: animation_default_1749103999.fbx (25852 bytes)
2025-06-05 14:33:04 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:33:04 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:33:04 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:33:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:33:38 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:33:39 | WARNING  | animation.professional_nlu:_load_spacy_model:60 - No spaCy model found, using blank model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:_load_sentiment_model:69 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:_load_action_classifier:79 - Using rule-based action classification instead of large model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:__init__:46 - Professional Animator NLU initialized
2025-06-05 14:33:39 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:33:39 | WARNING  | animation.professional_nlu:_load_spacy_model:60 - No spaCy model found, using blank model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:_load_sentiment_model:69 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:_load_action_classifier:79 - Using rule-based action classification instead of large model
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:__init__:46 - Professional Animator NLU initialized
2025-06-05 14:33:39 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:33:39 | INFO     | animation.professional_nlu:process_natural_language:340 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:33:39 | SUCCESS  | animation.professional_nlu:process_natural_language:380 - Successfully processed 1 actions
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:process_animation_request:65 - Step 2: Animator Function Processing
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:_apply_animator_functions:115 - Applying unified animator functions
2025-06-05 14:33:39 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 3: Generate Blender Animation Data
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:process_animation_request:79 - Step 4: Execute Blender Animation Generation
2025-06-05 14:33:39 | INFO     | animation.professional_pipeline:_execute_blender_generation:287 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749105219.json --output output/animations/animation_default_1749105219.fbx --format fbx
2025-06-05 14:33:40 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:299 - Blender animation generated: output/animations/animation_default_1749105219.fbx
2025-06-05 14:33:40 | SUCCESS  | animation.professional_pipeline:process_animation_request:95 - Animation generated successfully in 1.15s
2025-06-05 14:33:40 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:33:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:33:56 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749105219.fbx
2025-06-05 14:33:56 | WARNING  | backend.app:download_animation_file:332 - File not found: output/animations/animation_default_1749105219.fbx
2025-06-05 14:34:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:34:25 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:25 | WARNING  | animation.professional_nlu:_load_spacy_model:60 - No spaCy model found, using blank model
2025-06-05 14:34:25 | INFO     | animation.professional_nlu:_load_sentiment_model:69 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:34:25 | INFO     | animation.professional_nlu:_load_action_classifier:79 - Using rule-based action classification instead of large model
2025-06-05 14:34:25 | INFO     | animation.professional_nlu:__init__:46 - Professional Animator NLU initialized
2025-06-05 14:34:25 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:34:25 | INFO     | animation.professional_nlu:process_natural_language:340 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:25 | SUCCESS  | animation.professional_nlu:process_natural_language:380 - Successfully processed 1 actions
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:process_animation_request:65 - Step 2: Animator Function Processing
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:_apply_animator_functions:115 - Applying unified animator functions
2025-06-05 14:34:25 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 3: Generate Blender Animation Data
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:process_animation_request:79 - Step 4: Execute Blender Animation Generation
2025-06-05 14:34:25 | INFO     | animation.professional_pipeline:_execute_blender_generation:287 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749105265.json --output output/animations/animation_default_1749105265.fbx --format fbx
2025-06-05 14:34:26 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:299 - Blender animation generated: output/animations/animation_default_1749105265.fbx
2025-06-05 14:34:26 | SUCCESS  | animation.professional_pipeline:process_animation_request:95 - Animation generated successfully in 0.89s
2025-06-05 14:34:26 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:34:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:34:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:34:50 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:50 | WARNING  | animation.professional_nlu:_load_spacy_model:60 - No spaCy model found, using blank model
2025-06-05 14:34:50 | INFO     | animation.professional_nlu:_load_sentiment_model:69 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:34:50 | INFO     | animation.professional_nlu:_load_action_classifier:79 - Using rule-based action classification instead of large model
2025-06-05 14:34:50 | INFO     | animation.professional_nlu:__init__:46 - Professional Animator NLU initialized
2025-06-05 14:34:50 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:34:50 | INFO     | animation.professional_nlu:process_natural_language:340 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:34:50 | SUCCESS  | animation.professional_nlu:process_natural_language:380 - Successfully processed 1 actions
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:process_animation_request:65 - Step 2: Animator Function Processing
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:_apply_animator_functions:115 - Applying unified animator functions
2025-06-05 14:34:50 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 3: Generate Blender Animation Data
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:process_animation_request:79 - Step 4: Execute Blender Animation Generation
2025-06-05 14:34:50 | INFO     | animation.professional_pipeline:_execute_blender_generation:287 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749105290.json --output output/animations/animation_default_1749105290.fbx --format fbx
2025-06-05 14:34:51 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:299 - Blender animation generated: output/animations/animation_default_1749105290.fbx
2025-06-05 14:34:51 | SUCCESS  | animation.professional_pipeline:process_animation_request:95 - Animation generated successfully in 0.87s
2025-06-05 14:34:51 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:35:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:35:01 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749105290.fbx
2025-06-05 14:35:01 | WARNING  | backend.app:download_animation_file:332 - File not found: output/animations/animation_default_1749105290.fbx
2025-06-05 14:35:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:35:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:36:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:36:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:36:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:37:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:37:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:37:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:38:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:38:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:38:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:39:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:39:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:39:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:40:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:40:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:40:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:41:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:41:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:41:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:42:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:42:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:42:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:43:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:43:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:43:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:44:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:44:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:45:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:45:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:45:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:45:36 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:45:36 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:45:36 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:45:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:45:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:45:56 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:45:56 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:45:56 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:46:19 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:46:19 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:46:19 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:46:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:46:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:46:53 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:46:53 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:46:53 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:46:59 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:47:00 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:47:00 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:47:00 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:47:00 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:47:00 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:47:00 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:47:00 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:47:00 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:47:00 | INFO     | animation.professional_nlu:process_natural_language:351 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:47:00 | ERROR    | animation.professional_nlu:process_natural_language:437 - Error processing animation request: 3 validation errors for AnimationResponse
scenes.0
  Input should be a valid dictionary or instance of SceneSegment [type=model_type, input_value=SceneSegment(index=1, sce...ame=None, metadata={})]), input_type=SceneSegment]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
scenes.1
  Input should be a valid dictionary or instance of SceneSegment [type=model_type, input_value=SceneSegment(index=2, sce...ame=None, metadata={})]), input_type=SceneSegment]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
scenes.2
  Input should be a valid dictionary or instance of SceneSegment [type=model_type, input_value=SceneSegment(index=3, sce...ame=None, metadata={})]), input_type=SceneSegment]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-06-05 14:47:00 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:47:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:47:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:47:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:48:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:48:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:48:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:48:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:48:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:48:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:48:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:48:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:48:32 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:48:33 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:48:33 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:48:33 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:48:33 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:48:33 | INFO     | animation.professional_nlu:process_natural_language:351 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:48:33 | SUCCESS  | animation.professional_nlu:process_natural_language:431 - Successfully processed 3 actions in 3 scenes
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:_apply_animator_functions:147 - Applying unified animator functions
2025-06-05 14:48:33 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:48:33 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:48:33 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:48:33 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:48:33 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 14:48:33 | INFO     | animation.professional_pipeline:process_animation_request:111 - Step 4: Execute Blender Animation Generation
2025-06-05 14:48:33 | ERROR    | animation.professional_pipeline:process_animation_request:133 - Error in animation pipeline: Object of type SceneSegment is not JSON serializable
2025-06-05 14:48:33 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:48:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:49:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:49:08 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:49:08 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:49:08 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:49:21 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:49:22 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:49:22 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:49:22 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:49:22 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:49:22 | INFO     | animation.professional_nlu:process_natural_language:351 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:49:22 | SUCCESS  | animation.professional_nlu:process_natural_language:431 - Successfully processed 3 actions in 3 scenes
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:_apply_animator_functions:154 - Applying unified animator functions
2025-06-05 14:49:22 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:49:22 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:49:22 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:49:22 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:49:22 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:process_animation_request:118 - Step 4: Execute Blender Animation Generation
2025-06-05 14:49:22 | INFO     | animation.professional_pipeline:_execute_blender_generation:326 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749106162.json --output output/animations/animation_default_1749106162.fbx --format fbx
2025-06-05 14:49:23 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:338 - Blender animation generated: output/animations/animation_default_1749106162.fbx
2025-06-05 14:49:23 | SUCCESS  | animation.professional_pipeline:process_animation_request:134 - Animation generated successfully in 0.89s
2025-06-05 14:49:23 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:49:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:49:39 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749106162.fbx
2025-06-05 14:49:39 | WARNING  | backend.app:download_animation_file:332 - File not found: output/animations/animation_default_1749106162.fbx
2025-06-05 14:49:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:50:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:50:13 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749106162.fbx
2025-06-05 14:50:13 | WARNING  | backend.app:download_animation_file:332 - File not found: output/animations/animation_default_1749106162.fbx
2025-06-05 14:50:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:50:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:51:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:51:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:51:34 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:51:34 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:51:34 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:51:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:51:57 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:51:59 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:51:59 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:51:59 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:51:59 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:51:59 | INFO     | animation.professional_nlu:process_natural_language:351 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:51:59 | SUCCESS  | animation.professional_nlu:process_natural_language:431 - Successfully processed 3 actions in 3 scenes
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:_apply_animator_functions:172 - Applying unified animator functions
2025-06-05 14:51:59 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:51:59 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:51:59 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:51:59 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:51:59 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:process_animation_request:118 - Step 4: Execute Blender Animation Generation
2025-06-05 14:51:59 | INFO     | animation.professional_pipeline:_execute_blender_generation:344 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749106319.json --output output/animations/animation_default_1749106319.fbx --format fbx
2025-06-05 14:51:59 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:356 - Blender animation generated: output/animations/animation_default_1749106319.fbx
2025-06-05 14:51:59 | ERROR    | animation.professional_pipeline:process_animation_request:123 - FBX file not found after generation: output/animations/animation_default_1749106319.fbx
2025-06-05 14:51:59 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:52:01 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:52:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:52:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:53:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:53:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:53:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:54:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:54:22 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:54:22 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:54:22 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:54:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:54:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:54:53 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:54:54 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:54:54 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:54:54 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 14:54:54 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 14:54:54 | INFO     | animation.professional_nlu:process_natural_language:351 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:54:54 | SUCCESS  | animation.professional_nlu:process_natural_language:431 - Successfully processed 3 actions in 3 scenes
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:_apply_animator_functions:172 - Applying unified animator functions
2025-06-05 14:54:54 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:54:54 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:54:54 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 14:54:54 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:54:54 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: idle_stand -> idle_stand
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:process_animation_request:118 - Step 4: Execute Blender Animation Generation
2025-06-05 14:54:54 | INFO     | animation.professional_pipeline:_execute_blender_generation:344 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749106494.json --output output/animations/animation_default_1749106494.fbx --format fbx
2025-06-05 14:54:55 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:356 - Blender animation generated: output/animations/animation_default_1749106494.fbx
2025-06-05 14:54:55 | ERROR    | animation.professional_pipeline:process_animation_request:123 - FBX file not found after generation: output/animations/animation_default_1749106494.fbx
2025-06-05 14:54:55 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 14:55:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:55:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:55:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:56:17 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:56:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:56:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:57:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:57:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:57:35 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:57:35 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:57:35 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:57:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:58:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:58:11 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:58:11 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:58:11 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:58:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:58:37 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 14:58:37 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 14:58:37 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 14:58:41 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 14:58:42 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 14:58:42 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 14:58:42 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 14:58:42 | ERROR    | backend.app:generate_professional_animation:314 - Error in professional animation generation: SPECIAL
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 7
               └ <function _main at 0x10492cf40>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x104818fe0>
           └ <SpawnProcess name='SpawnProcess-14' parent=16334 started>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x104818540>
    └ <SpawnProcess name='SpawnProcess-14' parent=16334 started>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x104929cd0>, 'target': <bound method Server.run of <uvicorn.server.Server object...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-14' parent=16334 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-14' parent=16334 started>
    │    └ <function subprocess_started at 0x104f045e0>
    └ <SpawnProcess name='SpawnProcess-14' parent=16334 started>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 9000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x10508e010>>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 9000)>]
           │       │   │    └ <function Server.serve at 0x104ee32e0>
           │       │   └ <uvicorn.server.Server object at 0x10508e010>
           │       └ <function run at 0x104b8e3e0>
           └ <module 'asyncio' from '/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/__init__....
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x10489d990>
           │      └ <function Runner.run at 0x104c4e0c0>
           └ <asyncio.runners.Runner object at 0x10508d710>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-pac...
           │    │     └ <cyfunction Loop.run_until_complete at 0x1052a8520>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x10508d710>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x10529bdd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411d0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x107724410>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x10529bdd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411d0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411d0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x107727e50>
          └ <fastapi.applications.FastAPI object at 0x107724410>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x107749300>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x107724850>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x107727e50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:9000', 'connection': 'keep-alive', 'content-length': '603', 'sec-ch-ua-platform': '"macOS"', 'use...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x107749300>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x1076c0040>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x107724850>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x107724850>>, sen...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x10542f890>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x107724850>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x107724850>>, sen...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x107740bd0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x1077244d0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x10542f890>
          └ <function wrap_app_handling_exceptions at 0x105bf62a0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107749120>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x1077244d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107749120>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x1077244d0>>
          └ <fastapi.routing.APIRouter object at 0x1077244d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107749120>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x105bf7920>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107749120>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x107707a60>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107749120>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x107740c50>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x107749080>
          └ <function wrap_app_handling_exceptions at 0x105bf62a0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x107748f40>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1077411...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x107749080>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x107740c50>
                     └ <function get_request_handler.<locals>.app at 0x107707920>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x1048be020>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': AnimationRequest(text='生成一个 关羽登场，按照下面的场景生成\n1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时...
                 │         └ <function generate_professional_animation at 0x107707880>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

> File "/Users/<USER>/project/llm/motion-agent/backend/app.py", line 271, in generate_professional_animation
    from animation.professional_pipeline import ProfessionalAnimationPipeline

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/__init__.py", line 15, in <module>
    from .api import router as animation_router

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/api.py", line 17, in <module>
    animation_pipeline = ProfessionalAnimationPipeline()
                         └ <class 'animation.professional_pipeline.ProfessionalAnimationPipeline'>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_pipeline.py", line 33, in __init__
    self.nlu = ProfessionalAnimatorNLU()
    │          └ <class 'animation.professional_nlu.ProfessionalAnimatorNLU'>
    └ <animation.professional_pipeline.ProfessionalAnimationPipeline object at 0x12fcfc650>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_nlu.py", line 47, in __init__
    self.action_vocabulary = self._build_action_vocabulary()
    │                        │    └ <function ProfessionalAnimatorNLU._build_action_vocabulary at 0x12ff725c0>
    │                        └ <animation.professional_nlu.ProfessionalAnimatorNLU object at 0x11e8ad810>
    └ <animation.professional_nlu.ProfessionalAnimatorNLU object at 0x11e8ad810>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_nlu.py", line 137, in _build_action_vocabulary
    "type": AnimationType.SPECIAL,
            └ <enum 'AnimationType'>

  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/enum.py", line 786, in __getattr__
    raise AttributeError(name) from None
                         └ 'SPECIAL'

AttributeError: SPECIAL
2025-06-05 14:58:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:59:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:59:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 14:59:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:00:30 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:00:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:00:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:00:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:00:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:01:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:01:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:01:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:01:48 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:01:49 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 15:01:49 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:01:49 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 15:01:49 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 15:01:49 | INFO     | animation.professional_nlu:process_natural_language:382 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:01:49 | SUCCESS  | animation.professional_nlu:process_natural_language:462 - Successfully processed 3 actions in 3 scenes
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:_apply_animator_functions:172 - Applying unified animator functions
2025-06-05 15:01:49 | INFO     | animation.animator_functions:create_walk_cycle:65 - Creating walk cycle: None, AnimationIntensity.SLOW
2025-06-05 15:01:49 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: appear -> walk_cycle
2025-06-05 15:01:49 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: walk_cycle -> appear
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:process_animation_request:118 - Step 4: Execute Blender Animation Generation
2025-06-05 15:01:49 | INFO     | animation.professional_pipeline:_execute_blender_generation:344 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749106909.json --output output/animations/animation_default_1749106909.fbx --format fbx
2025-06-05 15:01:49 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:356 - Blender animation generated: output/animations/animation_default_1749106909.fbx
2025-06-05 15:01:49 | ERROR    | animation.professional_pipeline:process_animation_request:123 - FBX file not found after generation: output/animations/animation_default_1749106909.fbx
2025-06-05 15:01:49 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 15:02:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:02:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:02:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:03:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:03:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:03:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:03:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:03:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:03:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:03:49 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:03:49 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:03:49 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:03:55 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:03:55 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 15:03:55 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:03:55 | WARNING  | animation.professional_nlu:_load_spacy_model:71 - No spaCy model found, using blank model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:_load_sentiment_model:80 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:_load_action_classifier:90 - Using rule-based action classification instead of large model
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:__init__:57 - Professional Animator NLU initialized
2025-06-05 15:03:55 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 15:03:55 | INFO     | animation.professional_nlu:process_natural_language:382 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:03:55 | SUCCESS  | animation.professional_nlu:process_natural_language:462 - Successfully processed 3 actions in 3 scenes
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:_apply_animator_functions:172 - Applying unified animator functions
2025-06-05 15:03:55 | INFO     | animation.animator_functions:create_walk_cycle:65 - Creating walk cycle: None, AnimationIntensity.SLOW
2025-06-05 15:03:55 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: appear -> walk_cycle
2025-06-05 15:03:55 | INFO     | animation.animator_functions:create_transition:196 - Creating transition: walk_cycle -> appear
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:process_animation_request:102 - Step 3: Generate Blender Animation Data
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:process_animation_request:118 - Step 4: Execute Blender Animation Generation
2025-06-05 15:03:55 | INFO     | animation.professional_pipeline:_execute_blender_generation:344 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749107035.json --output output/animations/animation_default_1749107035.fbx --format fbx
2025-06-05 15:03:56 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:356 - Blender animation generated: output/animations/animation_default_1749107035.fbx
2025-06-05 15:03:56 | ERROR    | animation.professional_pipeline:process_animation_request:123 - FBX file not found after generation: output/animations/animation_default_1749107035.fbx
2025-06-05 15:03:56 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 15:04:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:04:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:04:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:04:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:04:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:04:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:05:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:05:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:05:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:06:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:06:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:06:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:06:28 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:06:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:06:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:07:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:07:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:07:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:08:24 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:08:31 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:31 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:31 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:08:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:34 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:34 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:34 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:38 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:38 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:38 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:08:42 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:42 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:42 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:44 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:44 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:44 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:46 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:46 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:46 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:48 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:48 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:48 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:50 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:50 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:50 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:52 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:52 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:52 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:54 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:54 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:54 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:08:59 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:08:59 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:08:59 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:00 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:00 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:00 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:02 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:09:05 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:05 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:05 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:05 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:05 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:05 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:07 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:07 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:07 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:09 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:09 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:09 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:12 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:12 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:12 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:15 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:15 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:15 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:23 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:09:23 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:09:23 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:09:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:09:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:10:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:10:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:11:12 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 15:11:12 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 15:11:12 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 15:11:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:11:18 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:11:19 | WARNING  | animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-05 15:11:19 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:11:19 | WARNING  | animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-05 15:11:19 | INFO     | animation.animator_functions:__init__:32 - Unified Professional Animator initialized
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 15:11:19 | INFO     | animation.professional_nlu:process_natural_language:287 - Processing animation request: 生成一个 关羽登场，按照下面的场景生成
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 15:11:19 | SUCCESS  | animation.professional_nlu:process_natural_language:316 - Successfully processed 1 actions
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:process_animation_request:65 - Step 2: Animator Function Processing
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:_apply_animator_functions:111 - Applying unified animator functions
2025-06-05 15:11:19 | INFO     | animation.animator_functions:create_idle_animation:168 - Creating idle animation: stand
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 3: Generate Blender Animation Data
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:process_animation_request:75 - Step 4: Execute Blender Animation Generation
2025-06-05 15:11:19 | INFO     | animation.professional_pipeline:_execute_blender_generation:283 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749107479.json --output output/animations/animation_default_1749107479.fbx --format fbx
2025-06-05 15:11:19 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:295 - Blender animation generated: output/animations/animation_default_1749107479.fbx
2025-06-05 15:11:19 | SUCCESS  | animation.professional_pipeline:process_animation_request:91 - Animation generated successfully in 0.56s
2025-06-05 15:11:19 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 15:11:30 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749107479.fbx
2025-06-05 15:11:30 | INFO     | backend.app:download_animation_file:339 - Serving file: animation_default_1749107479.fbx (25852 bytes)
2025-06-05 15:11:31 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 15:11:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:23:21 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 17:23:21 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 17:23:21 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 17:23:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:23:58 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:23:58 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 17:23:59 | WARNING  | animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-05 17:23:59 | INFO     | animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-05 17:23:59 | INFO     | animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-05 17:23:59 | ERROR    | backend.app:generate_professional_animation:314 - Error in professional animation generation: BOTH_HANDS
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 7
               └ <function _main at 0x1049dcf40>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x104850fe0>
           └ <SpawnProcess name='SpawnProcess-1' parent=43674 started>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x104850540>
    └ <SpawnProcess name='SpawnProcess-1' parent=43674 started>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x1049da410>, 'target': <bound method Server.run of <uvicorn.server.Server object...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=43674 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=43674 started>
    │    └ <function subprocess_started at 0x104f205e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=43674 started>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 9000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x1050ae350>>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 9000)>]
           │       │   │    └ <function Server.serve at 0x104f032e0>
           │       │   └ <uvicorn.server.Server object at 0x1050ae350>
           │       └ <function run at 0x104baa3e0>
           └ <module 'asyncio' from '/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/__init__....
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x10494d990>
           │      └ <function Runner.run at 0x104c6e0c0>
           └ <asyncio.runners.Runner object at 0x1050ad8d0>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-pac...
           │    │     └ <cyfunction Loop.run_until_complete at 0x1052c4520>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x1050ad8d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x1052d0550>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ead0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x107480e50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x1052d0550>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ead0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ead0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x10749c6d0>
          └ <fastapi.applications.FastAPI object at 0x107480e50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1050aa8e0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x107497f50>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x10749c6d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:9000', 'connection': 'keep-alive', 'content-length': '601', 'sec-ch-ua-platform': '"macOS"', 'use...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1050aa8e0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x106f280e0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x107497f50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x107497f50>>, sen...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1054425d0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x107497f50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x107497f50>>, sen...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x10749e5d0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x107480f90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x1054425d0>
          └ <function wrap_app_handling_exceptions at 0x106e4e340>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5260>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x107480f90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5260>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x107480f90>>
          └ <fastapi.routing.APIRouter object at 0x107480f90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5260>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x106e4f9c0>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5260>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x10746bb00>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5260>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x10749e610>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x1074a51c0>
          └ <function wrap_app_handling_exceptions at 0x106e4e340>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1074a5080>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x10749ea...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x1074a51c0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x10749e610>
                     └ <function get_request_handler.<locals>.app at 0x10746b9c0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x104972020>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': AnimationRequest(text='生成一个 关羽登场 的场景，要求如下：\n1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时...
                 │         └ <function generate_professional_animation at 0x10746b920>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

> File "/Users/<USER>/project/llm/motion-agent/backend/app.py", line 271, in generate_professional_animation
    from animation.professional_pipeline import ProfessionalAnimationPipeline

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/__init__.py", line 15, in <module>
    from .api import router as animation_router

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/api.py", line 17, in <module>
    animation_pipeline = ProfessionalAnimationPipeline()
                         └ <class 'animation.professional_pipeline.ProfessionalAnimationPipeline'>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_pipeline.py", line 33, in __init__
    self.nlu = ProfessionalAnimatorNLU()
    │          └ <class 'animation.professional_nlu.ProfessionalAnimatorNLU'>
    └ <animation.professional_pipeline.ProfessionalAnimationPipeline object at 0x12997e9d0>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_nlu.py", line 36, in __init__
    self.action_vocabulary = self._build_action_vocabulary()
    │                        │    └ <function ProfessionalAnimatorNLU._build_action_vocabulary at 0x1299da3e0>
    │                        └ <animation.professional_nlu.ProfessionalAnimatorNLU object at 0x12997fc50>
    └ <animation.professional_nlu.ProfessionalAnimatorNLU object at 0x12997fc50>

  File "/Users/<USER>/project/llm/motion-agent/backend/animation/professional_nlu.py", line 322, in _build_action_vocabulary
    "body_parts": [BodyPart.BOTH_HANDS],
                   └ <enum 'BodyPart'>

  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/enum.py", line 786, in __getattr__
    raise AttributeError(name) from None
                         └ 'BOTH_HANDS'

AttributeError: BOTH_HANDS
2025-06-05 17:24:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:24:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:25:35 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:25:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:26:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:27:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:27:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:28:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:28:41 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 17:28:41 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 17:28:41 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 17:28:50 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 17:28:50 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 17:28:50 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 17:28:52 | INFO     | backend.app:generate_professional_animation:264 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 17:28:53 | WARNING  | animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-05 17:28:53 | INFO     | animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 17:28:53 | WARNING  | animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-05 17:28:53 | INFO     | animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:__init__:45 - Professional Animation Pipeline initialized
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:process_animation_request:54 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:process_animation_request:58 - Step 1: Natural Language Understanding
2025-06-05 17:28:53 | INFO     | animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-05 17:28:53 | SUCCESS  | animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:process_animation_request:65 - Step 2: Animator Function Processing
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:_apply_animator_functions:111 - Applying unified animator functions
2025-06-05 17:28:53 | INFO     | animation.animator_functions:create_idle_animation:170 - Creating idle animation: stand
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:process_animation_request:71 - Step 3: Generate Blender Animation Data
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:process_animation_request:75 - Step 4: Execute Blender Animation Generation
2025-06-05 17:28:53 | INFO     | animation.professional_pipeline:_execute_blender_generation:283 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749115733.json --output output/animations/animation_default_1749115733.fbx --format fbx
2025-06-05 17:28:54 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:295 - Blender animation generated: output/animations/animation_default_1749115733.fbx
2025-06-05 17:28:54 | SUCCESS  | animation.professional_pipeline:process_animation_request:91 - Animation generated successfully in 1.09s
2025-06-05 17:28:54 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 17:28:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:29:05 | INFO     | backend.app:download_animation_file:324 - Download request for file: animation_default_1749115733.fbx
2025-06-05 17:29:05 | INFO     | backend.app:download_animation_file:339 - Serving file: animation_default_1749115733.fbx (25852 bytes)
2025-06-05 17:29:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:29:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:30:32 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:30:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:31:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:31:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:32:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:33:12 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:33:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:33:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:34:29 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:34:59 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:36:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:37:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:38:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:39:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:40:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:41:15 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 17:41:50 | INFO     | backend.app:startup_event:75 - Starting Motion Agent API server...
2025-06-05 17:41:50 | SUCCESS  | backend.app:startup_event:81 - Basic Motion Agent API initialized successfully
2025-06-05 17:41:50 | INFO     | backend.app:startup_event:82 - Motion Agent API is ready to serve requests
2025-06-05 17:42:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:43:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:44:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:45:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:46:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:47:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:48:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:49:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:50:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:51:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:52:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:53:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:54:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
2025-06-05 17:55:15 | DEBUG    | backend.app:health_check:351 - Health check endpoint accessed
