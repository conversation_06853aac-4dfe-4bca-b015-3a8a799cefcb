# Architectural thinking

## 项目设计思路
1. 打开网站，默认开启新的对话，thread 的概念，一个对话就是一个 thread，创建新聊天就是新的 thread
2. 任务管理
3. 功能管理

## 简版需求
你需要先去了解动画师的职能是什么，尤其是游戏开发动画师。然后实现以下需求
1. 将输入的自然语言使用LLM转换成标准的：如 type, direction , steps, angle, hand 等动画师的专业术语
2. 完成初级与中级动画师的职能：动捕清理、走跑跳、打击感等
3. 最后输出 .fbx 格式的文件。
4. 尽量使用开源软件或付费软件
5. 基于 MotionGPT + Motion Agent + MoDi + Blender + Python 实现、使用uv + poetry
6. 自然语言理解模块可以使用： spaCy + 自定义 NLU Pipeline（开源） + Haystack + Transformers（HuggingFace）

     
生成一个 关羽登场 的场景：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声


你需要先去了解动画师的职能是什么，尤其是游戏开发动画师。然后分析项目代码，是否满足以下需求：
1. 将输入的自然语言使用LLM转换成标准的：如 type, direction , steps, angle, hand 等动画师的专业术语
2. 完成动画师的职能：动捕清理、走跑跳、打击感等
3. 最后输出 .fbx 格式的文件，并且输出的文件能够正常在：3DMAX、maya
4. 尽量使用开源软件或付费软件
5. 基于 MotionGPT + Motion Agent + MoDi + Blender + Python 实现、使用uv + poetry
6. 自然语言理解模块可以使用： spaCy + 自定义 NLU Pipeline（开源） + Haystack + Transformers（HuggingFace）
示范场景：生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声



目前，直接“自然语言生成 FBX 动画”的开源项目很少，但有一些相关方向的项目和技术可以参考或组合实现你的目标：


#### 1.1 AnimateDiff
- 该项目利用扩散模型（Diffusion Model）生成2D或3D动画序列，有一定的自然语言到动作的能力，部分分支已尝试输出 3D 骨骼动画（可转 FBX）。
- 地址：https://github.com/guoyww/AnimateDiff

#### 1.2 Text2Motion
- 这个方向主要是“文本生成人体动作”（通常输出BVH、FBX或类似格式）。
- 推荐项目：
    - https://github.com/EricGuo5513/Text2Motion
    - https://github.com/Mathux/Text2Motion

#### 1.3 MotionGPT
- 结合 GPT 和动作生成，输入文本，输出动作序列（可转 FBX）。
- 地址：https://github.com/OpenMotionLab/MotionGPT

#### 1.4 HumanMotionGeneration-Awesome
- 整理了大量“自然语言/文本->动作”的相关论文和项目，适合查找合适的底层库：
- 地址：https://github.com/EricGuo5513/Awesome-HumanMotionGeneration

### 2. 技术实现流程建议

1. 使用如 MotionGPT、Text2Motion 这类模型，通过自然语言生成动作数据（如 BVH、JSON、CSV等格式的骨骼动画）。
2. 使用 Python 的 FBX SDK（如 Autodesk FBX SDK for Python 或第三方 pyfbx、fbx-converter 等）将生成的动作数据转为 .fbx 文件。

### 3. 关键 Python 库补充

- [fbx](https://github.com/ndee85/pyfbx)：Python 封装的 FBX 处理库。
- [autodesk-fbx](https://help.autodesk.com/view/FBX/2020/ENU/?guid=FBX_Developer_Help_scripting_python_html)：官方 Python SDK。

---

### 推荐你这样组合实现：

1. 用 MotionGPT 或 Text2Motion 输入自然语言，得到动作数据（骨骼帧、姿态等）。
2. 用 pyfbx 或 Autodesk FBX SDK for Python，把动作数据导出为 .fbx 文件。


## 1. 游戏开发动画师的核心职能

- **动作捕捉数据清理与修正**：去除噪声、修正骨骼穿插、平滑关键帧。
- **角色基础动作制作**：如走、跑、跳、攻击、受击等标准动作。
- **镜头动画与分镜**：包括镜头推进、摇移、景别切换等。
- **打击感与动画细节**：调整动作张力、起伏、攻击反馈，增强表现力。
- **动画导出与兼容**：输出 FBX 等格式，保证能在 3DMAX、Maya、游戏引擎中正常使用。

---

## 2. 实现流程设计

### Step 1. 自然语言到动画参数（NLU转动作描述）

- 利用 LLM（如 Transformers, Haystack）+ spaCy + 自定义 NLU Pipeline，将自然语言场景描述解析为标准动画参数（如：type、direction、steps、angle、hand、camera movement、environment）。
- 示例解析：
  - “全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行……”  
    ↓  
    - camera_type: full_shot
    - camera_motion: dolly_in (缓慢推进)
    - environment: sandstorm
    - character_action: female lead trudging forward
    - duration: 8s
    - sound: wind, drum (fast to slow)

### Step 2. 动作生成与动捕清理

- 采用 MotionGPT + Motion Agent + MoDi 对标准化参数做动作生成（如走、跑、跳、骑马、特写、镜头运动等）。
- 若有动捕数据，MoDi 可用于降噪、修正和清理。
- 动作骨骼输出通常为 BVH/FBX，易于兼容。

### Step 3. 动画合成与导出 FBX

- 用 Blender + Python 脚本自动化动画合成：导入动作数据，设定镜头与环境，合成场景动画，导出 FBX。
- Python 控制 Blender，确保输出的 FBX 文件兼容 3DMAX、Maya。
- Blender 支持命令行渲染和批处理，适合流水线自动化。

### Step 4. 软件与环境管理

- 全流程用 Python 实现，依赖 uv + poetry 管理环境和依赖。
- 相关模型和库尽量用开源（如 MotionGPT、MoDi、spaCy、Transformers），Blender 也是免费开源。
- 如需商业动画清理/生成软件，可适配 FBX/GLTF 格式。

---

## 3. 关键技术栈

- **自然语言解析**：spaCy + Transformers（HuggingFace）+ Haystack + 自定义 NLU
- **动作生成**：MotionGPT + Motion Agent + MoDi
- **动画合成与导出**：Blender + Python（`bpy` 库）
- **环境管理**：uv + poetry
- **输出格式**：FBX，确保兼容 3DMAX、Maya

---

## 4. 示例流程（伪代码/流程图）

1. **自然语言解析：**
    ```python
    # 使用 spaCy/Transformers/自定义NLP pipeline 解析场景描述
    nlu_result = nlu_pipeline("全景，推镜，黄沙漫天...")
    # 返回标准动词参数：如 camera_type, camera_motion, environment, character_action, duration, sound
    ```

2. **动作生成与清理：**
    ```python
    # MotionGPT生成动作骨骼
    motion_data = motiongpt.generate(nlu_result)
    # MoDi清理动捕数据
    clean_motion = modi.clean(motion_data)
    ```

3. **Blender自动生成动画并导出FBX：**
    ```python
    import bpy
    # 导入 clean_motion 到Blender
    # 设置场景、镜头、特效
    # bpy.ops.export_scene.fbx(filepath="output.fbx")
    ```

4. **环境依赖管理：**
    ```bash
    poetry add spacy transformers haystack motiongpt modi bpy
    uv venv
    ```

---

## 5. 开源工具/软件推荐

- [Blender](https://www.blender.org/)（动画合成与导出FBX）
- [MotionGPT](https://github.com/OpenMotionLab/MotionGPT)
- [MoDi](https://github.com/Mathux/MoDi)
- [spaCy](https://spacy.io/)
- [Haystack](https://github.com/deepset-ai/haystack)
- [Transformers](https://github.com/huggingface/transformers)
- [uv](https://github.com/astral-sh/uv)
- [poetry](https://python-poetry.org/)

---

## 6. 示例场景分解（以“关羽登场”片段为例）

### 场景1
- 镜头：全景，推镜，黄沙漫天
- 镜头运动：缓慢推进
- 主角动作：女主艰难前行
- 环境音效：风沙声、战鼓声（快到慢）
- 时长：8秒

### 场景2
- 镜头：全景，缓推镜
- 事件：黄沙散去，关羽骑士轮廓显现
- 背景音：战鼓声加强、士兵呐喊声
- 时长：2秒

---

## 7. 结论与建议

你的技术路线完全可行，建议逐步实现和测试以下模块：
1. NLU模块（先用 spaCy + Transformers 实现基本的动作/场景参数提取）
2. 动作生成（MotionGPT + MoDi）
3. Blender自动化与FBX导出
4. 环境依赖用 uv + poetry 管理

如需某一环节详细代码实现或现有开源项目的二次开发建议，请告知！
